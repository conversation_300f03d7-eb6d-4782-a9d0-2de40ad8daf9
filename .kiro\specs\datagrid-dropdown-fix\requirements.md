# Requirements Document

## Introduction

The TableEditorView in OracleMS contains a DataGrid with a ComboBox column for selecting data types. Currently, when users click on the dropdown menu in the "資料類型" (Data Type) column, no data appears in the dropdown list despite the DataTypes property being properly defined in the TableEditorViewModel. This feature aims to fix this issue to ensure that users can properly select data types from the dropdown menu.

## Requirements

### Requirement 1

**User Story:** As a database administrator, I want to be able to select data types from the dropdown menu in the table editor, so that I can properly define column data types without manual typing.

#### Acceptance Criteria

1. WHEN a user clicks on the data type dropdown in the DataGrid THEN the system SHALL display the list of available data types.
2. WHEN the DataGrid is loaded THEN the system SHALL properly bind the DataTypes collection from the ViewModel to the ComboBox items.
3. WHEN a data type is selected from the dropdown THEN the system SHALL update the column's DataType property with the selected value.
4. WHEN the DataGrid is initialized THEN the system SHALL ensure the ComboBox displays the currently selected data type.
5. IF a column's data type is changed THEN the system SHALL reflect this change in the UI immediately.

### Requirement 2

**User Story:** As a developer, I want to ensure proper data binding between the ViewModel and View, so that the application maintains MVVM pattern integrity.

#### Acceptance Criteria

1. WHEN the TableEditorView is loaded THEN the system SHALL establish proper binding between the DataGrid's ComboBox column and the ViewModel's DataTypes collection.
2. WHEN the ViewModel's DataTypes collection is updated THEN the system SHALL reflect these changes in the ComboBox options.
3. IF the binding context changes THEN the system SHALL update the ComboBox items accordingly.
4. WHEN implementing the fix THEN the system SHALL maintain separation of concerns according to the MVVM pattern.