using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;

namespace OracleMS.Converters
{
    /// <summary>
    /// 資料類型下拉選單的備用轉換器，當主要綁定失敗時提供預設值
    /// </summary>
    public class DataTypesFallbackConverter : IMultiValueConverter
    {
        /// <summary>
        /// 預設的 Oracle 資料類型清單
        /// </summary>
        private static readonly List<string> DefaultDataTypes = new List<string>
        {
            "VARCHAR2", "NVARCHAR2", "CHAR", "NCHAR", "NUMBER", "DATE", "TIMESTAMP", 
            "TIMESTAMP WITH TIME ZONE", "TIMESTAMP WITH LOCAL TIME ZONE", "INTERVAL YEAR TO MONTH", 
            "INTERVAL DAY TO SECOND", "BINARY_FLOAT", "BINARY_DOUBLE", "FLOAT", "LONG", "RAW", 
            "LONG RAW", "BLOB", "CLOB", "NCLOB", "BFILE", "ROWID", "UROWID", "XMLType"
        };

        /// <summary>
        /// 轉換多個綁定值，返回第一個有效的集合，如果都無效則返回預設值
        /// </summary>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // 尋找第一個非空且非 UnsetValue 的集合
            foreach (var value in values)
            {
                if (value != null && value != DependencyProperty.UnsetValue && value is IEnumerable<string> collection && collection.Any())
                {
                    return collection;
                }
            }

            // 如果都無效，返回預設值
            return DefaultDataTypes;
        }

        /// <summary>
        /// 不支援反向轉換
        /// </summary>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("DataTypesFallbackConverter does not support ConvertBack");
        }
    }
}