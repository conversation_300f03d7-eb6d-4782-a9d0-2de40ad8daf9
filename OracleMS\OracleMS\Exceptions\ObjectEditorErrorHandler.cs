using System;
using System.Data.Common;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Oracle.ManagedDataAccess.Client;
using OracleMS.Models;

namespace OracleMS.Exceptions
{
    /// <summary>
    /// 物件編輯器錯誤處理類別，提供統一的錯誤處理和訊息格式化邏輯
    /// </summary>
    public class ObjectEditorErrorHandler
    {
        private readonly ILogger _logger;

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="logger">記錄器</param>
        public ObjectEditorErrorHandler(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 處理例外並返回使用者友善的錯誤訊息
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectType">物件類型</param>
        /// <returns>使用者友善的錯誤訊息</returns>
        public string HandleException(Exception ex, string operation, string objectName, DatabaseObjectType objectType)
        {
            // 記錄錯誤
            LogError(ex, operation, objectName, objectType);

            // 格式化錯誤訊息
            return FormatErrorMessage(ex, operation, objectName, objectType);
        }

        /// <summary>
        /// 處理例外並返回使用者友善的錯誤訊息（非同步版本）
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectType">物件類型</param>
        /// <returns>使用者友善的錯誤訊息</returns>
        public async Task<string> HandleExceptionAsync(Exception ex, string operation, string objectName, DatabaseObjectType objectType)
        {
            // 記錄錯誤
            LogError(ex, operation, objectName, objectType);

            // 格式化錯誤訊息
            var errorMessage = FormatErrorMessage(ex, operation, objectName, objectType);

            // 模擬非同步操作，以便將來可以擴展為真正的非同步處理
            await Task.CompletedTask;

            return errorMessage;
        }

        /// <summary>
        /// 格式化錯誤訊息
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectType">物件類型</param>
        /// <returns>格式化的錯誤訊息</returns>
        public string FormatErrorMessage(Exception ex, string operation, string objectName, DatabaseObjectType objectType)
        {
            var objectTypeText = GetObjectTypeText(objectType);

            return ex switch
            {
                OracleException oracleEx => FormatOracleError(oracleEx, operation, objectName, objectTypeText),
                DbException dbEx => $"{objectTypeText}{operation}失敗：資料庫錯誤 - {dbEx.Message}",
                TimeoutException => $"{objectTypeText}{operation}失敗：操作逾時",
                UnauthorizedAccessException => $"{objectTypeText}{operation}失敗：沒有足夠的權限",
                OperationCanceledException => $"{objectTypeText}{operation}已取消",
                OracleManagementException omEx => $"{objectTypeText}{operation}失敗：{omEx.Message}",
                ArgumentException argEx => $"{objectTypeText}{operation}失敗：參數錯誤 - {argEx.Message}",
                InvalidOperationException invEx => $"{objectTypeText}{operation}失敗：無效的操作 - {invEx.Message}",
                _ => $"{objectTypeText}{operation}失敗：{ex.Message}"
            };
        }

        /// <summary>
        /// 格式化 Oracle 錯誤
        /// </summary>
        /// <param name="ex">Oracle 例外</param>
        /// <param name="operation">操作名稱</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectTypeText">物件類型文字</param>
        /// <returns>格式化的 Oracle 錯誤訊息</returns>
        private string FormatOracleError(OracleException ex, string operation, string objectName, string objectTypeText)
        {
            // 常見的 Oracle 錯誤代碼對應到使用者友善的訊息
            var userMessage = ex.Number switch
            {
                1 => $"物件名稱重複：已存在名為 '{objectName}' 的物件",
                942 => $"物件 '{objectName}' 不存在或沒有存取權限",
                1400 => "不可為空值的欄位未提供值",
                1401 => "插入的值太大，超過欄位允許的大小",
                1407 => "無法更新為 NULL 值",
                1438 => "數值超出允許範圍",
                1722 => "數值格式無效",
                2289 => $"序列 '{objectName}' 不存在",
                2291 => "違反參考完整性約束條件",
                2292 => "存在相依的子記錄",
                4080 => $"觸發器 '{objectName}' 編譯錯誤",
                6550 => "PL/SQL 編譯錯誤",
                12899 => "字串值太大",
                _ => ex.Message
            };

            return $"{objectTypeText}{operation}失敗：{userMessage} (ORA-{ex.Number})";
        }

        /// <summary>
        /// 記錄錯誤
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectType">物件類型</param>
        private void LogError(Exception ex, string operation, string objectName, DatabaseObjectType objectType)
        {
            var objectTypeText = GetObjectTypeText(objectType);
            var errorDetails = CollectErrorDetails(ex);

            _logger.LogError(ex, "{ObjectType}{Operation}失敗：{ObjectName}, 錯誤詳情: {ErrorDetails}",
                objectTypeText, operation, objectName, errorDetails);
        }

        /// <summary>
        /// 收集錯誤詳情
        /// </summary>
        /// <param name="ex">例外</param>
        /// <returns>錯誤詳情</returns>
        private string CollectErrorDetails(Exception ex)
        {
            var details = new StringBuilder();

            details.AppendLine($"例外類型: {ex.GetType().Name}");
            details.AppendLine($"訊息: {ex.Message}");

            if (ex is OracleException oracleEx)
            {
                details.AppendLine($"Oracle 錯誤代碼: {oracleEx.Number}");
                details.AppendLine($"Oracle 錯誤來源: {oracleEx.Source}");
                
                // 收集 Oracle 錯誤詳情
                for (int i = 0; i < oracleEx.Errors.Count; i++)
                {
                    var error = oracleEx.Errors[i];
                    details.AppendLine($"Oracle 錯誤 #{i + 1}: {error.Message} (代碼: {error.Number})");
                }
            }

            // 收集內部例外資訊
            var innerEx = ex.InnerException;
            if (innerEx != null)
            {
                details.AppendLine("內部例外:");
                details.AppendLine($"  類型: {innerEx.GetType().Name}");
                details.AppendLine($"  訊息: {innerEx.Message}");
            }

            // 收集堆疊追蹤
            if (!string.IsNullOrEmpty(ex.StackTrace))
            {
                details.AppendLine("堆疊追蹤:");
                details.AppendLine(ex.StackTrace);
            }

            return details.ToString();
        }

        /// <summary>
        /// 取得物件類型文字
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <returns>物件類型文字</returns>
        private string GetObjectTypeText(DatabaseObjectType objectType)
        {
            return objectType switch
            {
                DatabaseObjectType.Table => "資料表",
                DatabaseObjectType.View => "檢視表",
                DatabaseObjectType.Procedure => "預存程序",
                DatabaseObjectType.Function => "函數",
                DatabaseObjectType.Package => "套件",
                DatabaseObjectType.Sequence => "序列",
                DatabaseObjectType.Trigger => "觸發器",
                DatabaseObjectType.Index => "索引",
                _ => "物件"
            };
        }

        /// <summary>
        /// 建立錯誤結果
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectType">物件類型</param>
        /// <returns>錯誤結果</returns>
        public ErrorResult CreateErrorResult(Exception ex, string operation, string objectName, DatabaseObjectType objectType)
        {
            var errorMessage = FormatErrorMessage(ex, operation, objectName, objectType);
            var errorDetails = CollectErrorDetails(ex);

            return new ErrorResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorDetails = errorDetails,
                Exception = ex
            };
        }
    }

    /// <summary>
    /// 錯誤結果類別
    /// </summary>
    public class ErrorResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 錯誤詳情
        /// </summary>
        public string ErrorDetails { get; set; } = string.Empty;

        /// <summary>
        /// 例外
        /// </summary>
        public Exception? Exception { get; set; }
    }
}