using System.Data;
using OracleMS.Models;

namespace OracleMS.Interfaces;

/// <summary>
/// 資料庫連線提供者介面
/// </summary>
public interface IConnectionProvider
{
    /// <summary>
    /// 建立資料庫連線
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>資料庫連線物件</returns>
    Task<IDbConnection> CreateConnectionAsync(ConnectionInfo connectionInfo);

    /// <summary>
    /// 測試資料庫連線
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>連線測試結果</returns>
    Task<bool> TestConnectionAsync(ConnectionInfo connectionInfo);

    /// <summary>
    /// 建構連線字串
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>連線字串</returns>
    string BuildConnectionString(ConnectionInfo connectionInfo);
}