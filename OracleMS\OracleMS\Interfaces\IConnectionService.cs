using System.Data;
using OracleMS.Models;
using OracleMS.Services;

namespace OracleMS.Interfaces;

public interface IConnectionService
{
    Task<bool> TestConnectionAsync(ConnectionInfo connectionInfo);
    Task<IDbConnection> CreateConnectionAsync(ConnectionInfo connectionInfo);
    Task SaveConnectionAsync(ConnectionInfo connectionInfo);
    Task<IEnumerable<ConnectionInfo>> GetSavedConnectionsAsync();
    Task DeleteConnectionAsync(string connectionId);
    
    // Connection state management
    Task<IDbConnection> CreateManagedConnectionAsync(ConnectionInfo connectionInfo);
    void CloseConnection(string connectionId);
    IDbConnection? GetActiveConnection(string connectionId);
    IEnumerable<ConnectionInfo> GetActiveConnections();
    bool IsConnectionActive(string connectionId);
    void CloseAllConnections();
    
    event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;
}