using System.Data;
using OracleMS.Models;

namespace OracleMS.Interfaces;

public interface IDatabaseService
{
    Task<IEnumerable<DatabaseObject>> GetDatabaseObjectsAsync(IDbConnection connection, DatabaseObjectType type);
    Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string sql);
    Task<int> ExecuteNonQueryAsync(IDbConnection connection, string sql);
    Task<QueryResult> ExecuteScalarAsync(IDbConnection connection, string sql);
    Task<TableSchema> GetTableSchemaAsync(IDbConnection connection, string tableName);
    Task<QueryResult> ExecuteSqlAsync(IDbConnection connection, string sql, CancellationToken cancellationToken = default);
    Task<IEnumerable<QueryResult>> ExecuteMultipleSqlAsync(IDbConnection connection, IEnumerable<string> sqlStatements, bool useTransaction = true);
    Task<QueryResult> GetTableDataAsync(IDbConnection connection, string tableName, int pageNumber = 1, int pageSize = 1000, string? whereClause = null, string? orderBy = null);
    
    // Transaction-aware execution methods
    Task<QueryResult> ExecuteSqlWithTransactionAsync(
        IDbConnection connection,
        string sql,
        CancellationToken cancellationToken = default,
        bool skipTransactionManagement = false);

    Task<IEnumerable<QueryResult>> ExecuteMultipleSqlWithTransactionAsync(
        IDbConnection connection,
        IEnumerable<string> sqlStatements,
        CancellationToken cancellationToken = default);

    // 交易管理方法
    bool HasActiveTransaction { get; }
    TransactionState CurrentTransactionState { get; }
    event EventHandler<TransactionStateChangedEventArgs>? TransactionStateChanged;
    Task BeginTransactionAsync(IDbConnection connection);
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}