using System.Data;

namespace OracleMS.Interfaces;

/// <summary>
/// Interface for managing database transactions with automatic lifecycle handling
/// </summary>
public interface ITransactionManager : IDisposable
{
    /// <summary>
    /// Gets a value indicating whether there is an active transaction
    /// </summary>
    bool HasActiveTransaction { get; }

    /// <summary>
    /// Gets the current transaction state
    /// </summary>
    TransactionState CurrentState { get; }

    /// <summary>
    /// Gets the current active transaction, if any
    /// </summary>
    IDbTransaction? CurrentTransaction { get; }

    /// <summary>
    /// Begins a new transaction on the specified connection
    /// </summary>
    /// <param name="connection">The database connection</param>
    /// <returns>The created transaction</returns>
    Task<IDbTransaction> BeginTransactionAsync(IDbConnection connection);

    /// <summary>
    /// Commits the current active transaction
    /// </summary>
    Task CommitAsync();

    /// <summary>
    /// Rolls back the current active transaction
    /// </summary>
    Task RollbackAsync();

    /// <summary>
    /// Determines if the given SQL statement should trigger an auto-commit
    /// </summary>
    /// <param name="sql">The SQL statement to evaluate</param>
    /// <returns>True if auto-commit should occur, false otherwise</returns>
    bool ShouldAutoCommit(string sql);

    /// <summary>
    /// Executes auto-commit logic for DDL statements if needed
    /// </summary>
    /// <param name="sql">The SQL statement that was executed</param>
    /// <returns>True if auto-commit was performed, false otherwise</returns>
    Task<bool> AutoCommitIfNeededAsync(string sql);

    /// <summary>
    /// Resets the transaction manager state
    /// </summary>
    void Reset();

    /// <summary>
    /// Resets the transaction state when a new connection is established
    /// </summary>
    /// <param name="newConnection">The new database connection</param>
    void ResetForNewConnection(IDbConnection newConnection);

    /// <summary>
    /// Gets or sets the transaction timeout in seconds (0 = no timeout)
    /// </summary>
    int TransactionTimeoutSeconds { get; set; }

    /// <summary>
    /// Gets the time when the current transaction was started, if any
    /// </summary>
    DateTime? TransactionStartTime { get; }

    /// <summary>
    /// Checks if the current transaction has exceeded the timeout period
    /// </summary>
    /// <returns>True if transaction has timed out, false otherwise</returns>
    bool IsTransactionTimedOut();

    /// <summary>
    /// Event raised when the transaction state changes
    /// </summary>
    event EventHandler<TransactionStateChangedEventArgs>? StateChanged;
}

/// <summary>
/// Represents the state of a transaction
/// </summary>
public enum TransactionState
{
    /// <summary>
    /// No transaction is active
    /// </summary>
    None,

    /// <summary>
    /// A transaction is currently active
    /// </summary>
    Active,

    /// <summary>
    /// The transaction has been committed
    /// </summary>
    Committed,

    /// <summary>
    /// The transaction has been rolled back
    /// </summary>
    RolledBack,

    /// <summary>
    /// An error occurred during transaction operations
    /// </summary>
    Error
}

/// <summary>
/// Event arguments for transaction state changes
/// </summary>
public class TransactionStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// Gets the previous transaction state
    /// </summary>
    public TransactionState PreviousState { get; set; }

    /// <summary>
    /// Gets the new transaction state
    /// </summary>
    public TransactionState NewState { get; set; }

    /// <summary>
    /// Gets an optional message describing the state change
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Gets the error that caused the state change, if any
    /// </summary>
    public Exception? Error { get; set; }

    /// <summary>
    /// Initializes a new instance of the TransactionStateChangedEventArgs class
    /// </summary>
    /// <param name="previousState">The previous state</param>
    /// <param name="newState">The new state</param>
    /// <param name="message">Optional message</param>
    /// <param name="error">Optional error</param>
    public TransactionStateChangedEventArgs(
        TransactionState previousState, 
        TransactionState newState, 
        string? message = null, 
        Exception? error = null)
    {
        PreviousState = previousState;
        NewState = newState;
        Message = message;
        Error = error;
    }
}