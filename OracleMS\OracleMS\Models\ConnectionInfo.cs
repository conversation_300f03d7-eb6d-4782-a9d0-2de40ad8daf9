namespace OracleMS.Models;

public class ConnectionInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Server { get; set; } = string.Empty;
    public int Port { get; set; } = 1521;
    public string ServiceName { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty; // 加密儲存
    public ConnectionType Type { get; set; } = ConnectionType.Basic;
    public bool SavePassword { get; set; }
    public DateTime LastConnected { get; set; }
    public string Description { get; set; } = string.Empty;
    public int ConnectionTimeout { get; set; } = 30;
    public bool AutoConnect { get; set; }

    /// <summary>
    /// 驗證連線資訊是否完整且有效
    /// </summary>
    /// <returns>驗證結果和錯誤訊息</returns>
    public (bool IsValid, string ErrorMessage) Validate()
    {
        //if (string.IsNullOrWhiteSpace(Name))
        //    return (false, "連線名稱不能為空");

        //if (string.IsNullOrWhiteSpace(Server))
        //    return (false, "伺服器位址不能為空");

        if (Port <= 0 || Port > 65535)
            return (false, "埠號必須在 1-65535 範圍內");

        if (string.IsNullOrWhiteSpace(ServiceName))
            return (false, "服務名稱不能為空");

        if (string.IsNullOrWhiteSpace(Username))
            return (false, "使用者名稱不能為空");

        if (string.IsNullOrWhiteSpace(Password))
            return (false, "密碼不能為空");

        Id = ServiceName + "@" + Server;
        return (true, string.Empty);
    }

    /// <summary>
    /// 建構 Oracle 連線字串
    /// </summary>
    /// <returns>連線字串</returns>
    public string BuildConnectionString()
    {
        return Type switch
        {
            ConnectionType.Basic => $"Data Source={Server}:{Port}/{ServiceName};User Id={Username};Password={Password};",
            ConnectionType.TNS => $"Data Source={ServiceName};User Id={Username};Password={Password};",
            ConnectionType.Advanced => $"User Id={Username};Password={Password};Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={Server})(PORT={Port}))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME={ServiceName})))",
            _ => $"Data Source={Server}:{Port}/{ServiceName};User Id={Username};Password={Password};"
        };
    }

    /// <summary>
    /// 複製連線資訊（不包含密碼）
    /// </summary>
    /// <returns>不含密碼的連線資訊副本</returns>
    public ConnectionInfo CloneWithoutPassword()
    {
        return new ConnectionInfo
        {
            Id = Id,
            Name = Name,
            Server = Server,
            Port = Port,
            ServiceName = ServiceName,
            Username = Username,
            Password = string.Empty,
            Type = Type,
            SavePassword = false,
            LastConnected = LastConnected
        };
    }
}

public enum ConnectionType
{
    Basic,
    TNS,
    Advanced
}