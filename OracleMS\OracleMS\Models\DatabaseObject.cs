namespace OracleMS.Models;

public class DatabaseObject
{
    public string Name { get; set; } = string.Empty;
    public string Owner { get; set; } = string.Empty;
    public DatabaseObjectType Type { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime ModifiedDate { get; set; }
    public string Status { get; set; } = string.Empty;
}

public enum DatabaseObjectType
{
    Table,
    View,
    Procedure,
    Function,
    Package,
    Sequence,
    Trigger,
    Index
}