using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace OracleMS.Models
{
    /// <summary>
    /// 編輯器狀態管理器，負責編輯器狀態的持久化和恢復
    /// </summary>
    public class EditorStateManager : IDisposable
    {
        private readonly ILogger _logger;
        private readonly string _stateDirectory;
        private readonly Dictionary<string, EditorState> _stateCache = new Dictionary<string, EditorState>();
        private bool _isDisposed;

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="logger">記錄器</param>
        public EditorStateManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _stateDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "OracleMS", "EditorStates");
            
            // 確保狀態目錄存在
            EnsureStateDirectoryExists();
        }

        /// <summary>
        /// 儲存編輯器狀態
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="state">編輯器狀態</param>
        /// <returns>非同步工作</returns>
        public async Task SaveStateAsync(DatabaseObjectType objectType, string objectName, EditorState state)
        {
            if (string.IsNullOrEmpty(objectName))
                throw new ArgumentException("物件名稱不能為空", nameof(objectName));

            var stateKey = GetStateKey(objectType, objectName);
            
            try
            {
                // 更新快取
                _stateCache[stateKey] = state;
                
                // 序列化狀態
                var json = JsonSerializer.Serialize(state, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                // 儲存到檔案
                var filePath = GetStateFilePath(stateKey);
                await File.WriteAllTextAsync(filePath, json);
                
                _logger.LogDebug("已儲存編輯器狀態: {ObjectType} {ObjectName}", objectType, objectName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "儲存編輯器狀態失敗: {ObjectType} {ObjectName}", objectType, objectName);
            }
        }

        /// <summary>
        /// 載入編輯器狀態
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <param name="objectName">物件名稱</param>
        /// <returns>編輯器狀態，如果不存在則返回 null</returns>
        public async Task<EditorState?> LoadStateAsync(DatabaseObjectType objectType, string objectName)
        {
            if (string.IsNullOrEmpty(objectName))
                throw new ArgumentException("物件名稱不能為空", nameof(objectName));

            var stateKey = GetStateKey(objectType, objectName);
            
            // 檢查快取
            if (_stateCache.TryGetValue(stateKey, out var cachedState))
            {
                return cachedState;
            }
            
            try
            {
                var filePath = GetStateFilePath(stateKey);
                
                // 檢查檔案是否存在
                if (!File.Exists(filePath))
                {
                    return null;
                }
                
                // 讀取檔案
                var json = await File.ReadAllTextAsync(filePath);
                
                // 反序列化
                var state = JsonSerializer.Deserialize<EditorState>(json);
                
                // 更新快取
                if (state != null)
                {
                    _stateCache[stateKey] = state;
                }
                
                _logger.LogDebug("已載入編輯器狀態: {ObjectType} {ObjectName}", objectType, objectName);
                
                return state;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "載入編輯器狀態失敗: {ObjectType} {ObjectName}", objectType, objectName);
                return null;
            }
        }

        /// <summary>
        /// 刪除編輯器狀態
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <param name="objectName">物件名稱</param>
        /// <returns>非同步工作</returns>
        public async Task DeleteStateAsync(DatabaseObjectType objectType, string objectName)
        {
            if (string.IsNullOrEmpty(objectName))
                throw new ArgumentException("物件名稱不能為空", nameof(objectName));

            var stateKey = GetStateKey(objectType, objectName);
            
            try
            {
                // 從快取中移除
                _stateCache.Remove(stateKey);
                
                // 刪除檔案
                var filePath = GetStateFilePath(stateKey);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    await Task.CompletedTask; // 保持非同步方法簽章
                    
                    _logger.LogDebug("已刪除編輯器狀態: {ObjectType} {ObjectName}", objectType, objectName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除編輯器狀態失敗: {ObjectType} {ObjectName}", objectType, objectName);
            }
        }

        /// <summary>
        /// 清理過期的編輯器狀態
        /// </summary>
        /// <param name="maxAgeInDays">最大保留天數</param>
        /// <returns>非同步工作</returns>
        public async Task CleanupStatesAsync(int maxAgeInDays = 7)
        {
            try
            {
                var directory = new DirectoryInfo(_stateDirectory);
                if (!directory.Exists)
                {
                    return;
                }
                
                var cutoffDate = DateTime.Now.AddDays(-maxAgeInDays);
                var files = directory.GetFiles("*.json");
                
                foreach (var file in files)
                {
                    if (file.LastWriteTime < cutoffDate)
                    {
                        file.Delete();
                        _logger.LogDebug("已刪除過期的編輯器狀態檔案: {FileName}", file.Name);
                    }
                }
                
                await Task.CompletedTask; // 保持非同步方法簽章
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理過期的編輯器狀態失敗");
            }
        }

        /// <summary>
        /// 確保狀態目錄存在
        /// </summary>
        private void EnsureStateDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(_stateDirectory))
                {
                    Directory.CreateDirectory(_stateDirectory);
                    _logger.LogDebug("已創建編輯器狀態目錄: {Directory}", _stateDirectory);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建編輯器狀態目錄失敗: {Directory}", _stateDirectory);
            }
        }

        /// <summary>
        /// 取得狀態檔案路徑
        /// </summary>
        /// <param name="stateKey">狀態鍵值</param>
        /// <returns>檔案路徑</returns>
        private string GetStateFilePath(string stateKey)
        {
            return Path.Combine(_stateDirectory, $"{stateKey}.json");
        }

        /// <summary>
        /// 取得狀態鍵值
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <param name="objectName">物件名稱</param>
        /// <returns>狀態鍵值</returns>
        private string GetStateKey(DatabaseObjectType objectType, string objectName)
        {
            // 將物件名稱中的特殊字元替換為底線，避免檔案名稱問題
            var sanitizedName = string.Join("_", objectName.Split(Path.GetInvalidFileNameChars()));
            return $"{objectType}_{sanitizedName}";
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        /// <param name="disposing">是否正在釋放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    // 釋放受控資源
                    _stateCache.Clear();
                }

                _isDisposed = true;
            }
        }
    }

    /// <summary>
    /// 編輯器狀態類別
    /// </summary>
    public class EditorState
    {
        /// <summary>
        /// 物件類型
        /// </summary>
        public DatabaseObjectType ObjectType { get; set; }
        
        /// <summary>
        /// 物件名稱
        /// </summary>
        public string ObjectName { get; set; } = string.Empty;
        
        /// <summary>
        /// 最後修改時間
        /// </summary>
        public DateTime LastModified { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 游標位置
        /// </summary>
        public int CursorPosition { get; set; }
        
        /// <summary>
        /// 選取的索引標籤
        /// </summary>
        public int SelectedTabIndex { get; set; }
        
        /// <summary>
        /// 自訂狀態資料
        /// </summary>
        public Dictionary<string, object> CustomState { get; set; } = new Dictionary<string, object>();
    }
}