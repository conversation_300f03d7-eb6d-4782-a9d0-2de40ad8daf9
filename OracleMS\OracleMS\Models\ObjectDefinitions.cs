using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace OracleMS.Models
{
    /// <summary>
    /// 資料表定義模型
    /// </summary>
    public class TableDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _owner = string.Empty;
        private string _comments = string.Empty;
        private ObservableCollection<ColumnDefinition> _columns = new();
        private ObservableCollection<IndexDefinition> _indexes = new();
        private ObservableCollection<ConstraintDefinition> _constraints = new();
        private ObservableCollection<TriggerDefinition> _triggers = new();

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料表擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 資料表註解
        /// </summary>
        public string Comments
        {
            get => _comments;
            set => SetProperty(ref _comments, value);
        }

        /// <summary>
        /// 資料表欄位集合
        /// </summary>
        public ObservableCollection<ColumnDefinition> Columns
        {
            get => _columns;
            set => SetProperty(ref _columns, value);
        }

        /// <summary>
        /// 資料表索引集合
        /// </summary>
        public ObservableCollection<IndexDefinition> Indexes
        {
            get => _indexes;
            set => SetProperty(ref _indexes, value);
        }

        /// <summary>
        /// 資料表約束條件集合
        /// </summary>
        public ObservableCollection<ConstraintDefinition> Constraints
        {
            get => _constraints;
            set => SetProperty(ref _constraints, value);
        }

        /// <summary>
        /// 資料表觸發器集合
        /// </summary>
        public ObservableCollection<TriggerDefinition> Triggers
        {
            get => _triggers;
            set => SetProperty(ref _triggers, value);
        }

        /// <summary>
        /// 驗證資料表定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("資料表名稱不能為空");
            }

            if (Columns.Count == 0)
            {
                result.AddError("資料表必須至少有一個欄位");
            }

            foreach (var column in Columns)
            {
                var columnValidation = column.Validate();
                if (!columnValidation.IsValid)
                {
                    result.AddErrors(columnValidation.Errors);
                }
            }

            return result;
        }
    }

    /// <summary>
    /// 欄位定義模型
    /// </summary>
    public class ColumnDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _dataType = string.Empty;
        private int? _length;
        private int? _precision;
        private int? _scale;
        private bool _isNullable;
        private string _defaultValue = string.Empty;
        private string _comments = string.Empty;

        /// <summary>
        /// 欄位名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料類型
        /// </summary>
        public string DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        /// <summary>
        /// 長度 (適用於 VARCHAR2, CHAR 等)
        /// </summary>
        public int? Length
        {
            get => _length;
            set => SetProperty(ref _length, value);
        }

        /// <summary>
        /// 精度 (適用於 NUMBER)
        /// </summary>
        public int? Precision
        {
            get => _precision;
            set => SetProperty(ref _precision, value);
        }

        /// <summary>
        /// 小數位數 (適用於 NUMBER)
        /// </summary>
        public int? Scale
        {
            get => _scale;
            set => SetProperty(ref _scale, value);
        }

        /// <summary>
        /// 是否允許 NULL
        /// </summary>
        public bool IsNullable
        {
            get => _isNullable;
            set => SetProperty(ref _isNullable, value);
        }

        /// <summary>
        /// 預設值
        /// </summary>
        public string DefaultValue
        {
            get => _defaultValue;
            set => SetProperty(ref _defaultValue, value);
        }

        /// <summary>
        /// 欄位註解
        /// </summary>
        public string Comments
        {
            get => _comments;
            set => SetProperty(ref _comments, value);
        }

        /// <summary>
        /// 驗證欄位定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError($"欄位名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(DataType))
            {
                result.AddError($"欄位 {Name} 的資料類型不能為空");
            }

            return result;
        }

        /// <summary>
        /// 取得完整的資料類型定義，包含長度、精度和小數位數
        /// </summary>
        /// <returns>完整的資料類型定義</returns>
        public string GetFullDataType()
        {
            if (string.IsNullOrWhiteSpace(DataType))
                return string.Empty;

            var dataTypeUpper = DataType.ToUpper();

            if ((dataTypeUpper == "VARCHAR2" || dataTypeUpper == "CHAR" || dataTypeUpper == "NVARCHAR2" || dataTypeUpper == "NCHAR") && Length.HasValue)
            {
                return $"{dataTypeUpper}({Length})";
            }
            else if (dataTypeUpper == "NUMBER" && Precision.HasValue)
            {
                if (Scale.HasValue && Scale.Value > 0)
                {
                    return $"{dataTypeUpper}({Precision},{Scale})";
                }
                else
                {
                    return $"{dataTypeUpper}({Precision})";
                }
            }
            else
            {
                return dataTypeUpper;
            }
        }
    }

    /// <summary>
    /// 約束條件定義模型
    /// </summary>
    public class ConstraintDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private ConstraintType _type;
        private string _tableName = string.Empty;
        private List<string> _columns = new();
        private string _referencedTable = string.Empty;
        private List<string> _referencedColumns = new();
        private string _checkCondition = string.Empty;
        private bool _isEnabled = true;

        /// <summary>
        /// 約束條件名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 約束條件類型
        /// </summary>
        public ConstraintType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string TableName
        {
            get => _tableName;
            set => SetProperty(ref _tableName, value);
        }

        /// <summary>
        /// 約束條件涉及的欄位
        /// </summary>
        public List<string> Columns
        {
            get => _columns;
            set => SetProperty(ref _columns, value);
        }

        /// <summary>
        /// 參考的資料表 (適用於外鍵)
        /// </summary>
        public string ReferencedTable
        {
            get => _referencedTable;
            set => SetProperty(ref _referencedTable, value);
        }

        /// <summary>
        /// 參考的欄位 (適用於外鍵)
        /// </summary>
        public List<string> ReferencedColumns
        {
            get => _referencedColumns;
            set => SetProperty(ref _referencedColumns, value);
        }

        /// <summary>
        /// 檢查條件 (適用於 CHECK 約束)
        /// </summary>
        public string CheckCondition
        {
            get => _checkCondition;
            set => SetProperty(ref _checkCondition, value);
        }

        /// <summary>
        /// 約束條件是否啟用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// 驗證約束條件定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("約束條件名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(TableName))
            {
                result.AddError("資料表名稱不能為空");
            }

            if (Type == ConstraintType.PrimaryKey || Type == ConstraintType.Unique || Type == ConstraintType.ForeignKey)
            {
                if (Columns.Count == 0)
                {
                    result.AddError($"約束條件 {Name} 必須指定至少一個欄位");
                }
            }

            if (Type == ConstraintType.ForeignKey)
            {
                if (string.IsNullOrWhiteSpace(ReferencedTable))
                {
                    result.AddError($"外鍵約束條件 {Name} 必須指定參考的資料表");
                }

                if (ReferencedColumns.Count == 0)
                {
                    result.AddError($"外鍵約束條件 {Name} 必須指定參考的欄位");
                }

                if (Columns.Count != ReferencedColumns.Count)
                {
                    result.AddError($"外鍵約束條件 {Name} 的欄位數量必須與參考欄位數量相同");
                }
            }

            if (Type == ConstraintType.Check && string.IsNullOrWhiteSpace(CheckCondition))
            {
                result.AddError($"檢查約束條件 {Name} 必須指定檢查條件");
            }

            return result;
        }
    }

    /// <summary>
    /// 約束條件類型
    /// </summary>
    public enum ConstraintType
    {
        PrimaryKey,
        Unique,
        ForeignKey,
        Check
    }

    /// <summary>
    /// 索引定義模型
    /// </summary>
    public class IndexDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _tableName = string.Empty;
        private string _owner = string.Empty;
        private IndexType _type;
        private List<IndexColumnDefinition> _columns = new();
        private bool _isUnique;
        private string _tablespace = string.Empty;
        private string _status = string.Empty;
        private DateTime _lastAnalyzed;
        private int _distinctKeys;
        private int _leafBlocks;
        private int _clustering;

        /// <summary>
        /// 索引名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string TableName
        {
            get => _tableName;
            set => SetProperty(ref _tableName, value);
        }

        /// <summary>
        /// 索引擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 索引類型
        /// </summary>
        public IndexType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 索引欄位集合
        /// </summary>
        public List<IndexColumnDefinition> Columns
        {
            get => _columns;
            set => SetProperty(ref _columns, value);
        }

        /// <summary>
        /// 是否為唯一索引
        /// </summary>
        public bool IsUnique
        {
            get => _isUnique;
            set => SetProperty(ref _isUnique, value);
        }

        /// <summary>
        /// 表空間名稱
        /// </summary>
        public string Tablespace
        {
            get => _tablespace;
            set => SetProperty(ref _tablespace, value);
        }

        /// <summary>
        /// 索引狀態
        /// </summary>
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 最後分析時間
        /// </summary>
        public DateTime LastAnalyzed
        {
            get => _lastAnalyzed;
            set => SetProperty(ref _lastAnalyzed, value);
        }

        /// <summary>
        /// 不同鍵值數量
        /// </summary>
        public int DistinctKeys
        {
            get => _distinctKeys;
            set => SetProperty(ref _distinctKeys, value);
        }

        /// <summary>
        /// 葉節點數量
        /// </summary>
        public int LeafBlocks
        {
            get => _leafBlocks;
            set => SetProperty(ref _leafBlocks, value);
        }

        /// <summary>
        /// 叢集因子
        /// </summary>
        public int Clustering
        {
            get => _clustering;
            set => SetProperty(ref _clustering, value);
        }

        /// <summary>
        /// 驗證索引定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("索引名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(TableName))
            {
                result.AddError("資料表名稱不能為空");
            }

            if (Columns.Count == 0)
            {
                result.AddError($"索引 {Name} 必須指定至少一個欄位");
            }

            return result;
        }
    }

    /// <summary>
    /// 索引欄位定義
    /// </summary>
    public class IndexColumnDefinition : NotifyPropertyChangedBase
    {
        private string _columnName = string.Empty;
        private bool _isDescending;
        private int _position;

        /// <summary>
        /// 欄位名稱
        /// </summary>
        public string ColumnName
        {
            get => _columnName;
            set => SetProperty(ref _columnName, value);
        }

        /// <summary>
        /// 是否為遞減排序
        /// </summary>
        public bool IsDescending
        {
            get => _isDescending;
            set => SetProperty(ref _isDescending, value);
        }

        /// <summary>
        /// 欄位在索引中的位置
        /// </summary>
        public int Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }
    }

    /// <summary>
    /// 索引類型
    /// </summary>
    public enum IndexType
    {
        Normal,
        Bitmap,
        Function,
        Domain,
        Spatial
    }

    /// <summary>
    /// 觸發器定義模型
    /// </summary>
    public class TriggerDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _tableName = string.Empty;
        private string _owner = string.Empty;
        private TriggerType _type;
        private TriggerEvent _event;
        private TriggerTiming _timing;
        private string _condition = string.Empty;
        private string _body = string.Empty;
        private bool _isEnabled = true;
        private string _status = string.Empty;

        /// <summary>
        /// 觸發器名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string TableName
        {
            get => _tableName;
            set => SetProperty(ref _tableName, value);
        }

        /// <summary>
        /// 觸發器擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 觸發器類型
        /// </summary>
        public TriggerType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 觸發事件
        /// </summary>
        public TriggerEvent Event
        {
            get => _event;
            set => SetProperty(ref _event, value);
        }

        /// <summary>
        /// 觸發時機
        /// </summary>
        public TriggerTiming Timing
        {
            get => _timing;
            set => SetProperty(ref _timing, value);
        }

        /// <summary>
        /// 觸發條件
        /// </summary>
        public string Condition
        {
            get => _condition;
            set => SetProperty(ref _condition, value);
        }

        /// <summary>
        /// 觸發器主體
        /// </summary>
        public string Body
        {
            get => _body;
            set => SetProperty(ref _body, value);
        }

        /// <summary>
        /// 觸發器是否啟用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// 觸發器狀態
        /// </summary>
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 驗證觸發器定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("觸發器名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(TableName))
            {
                result.AddError("資料表名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(Body))
            {
                result.AddError($"觸發器 {Name} 的主體不能為空");
            }

            return result;
        }
    }

    /// <summary>
    /// 觸發器類型
    /// </summary>
    public enum TriggerType
    {
        Row,
        Statement
    }

    /// <summary>
    /// 觸發事件
    /// </summary>
    public enum TriggerEvent
    {
        Insert,
        Update,
        Delete,
        InsertOrUpdate,
        InsertOrDelete,
        UpdateOrDelete,
        InsertOrUpdateOrDelete
    }

    /// <summary>
    /// 觸發時機
    /// </summary>
    public enum TriggerTiming
    {
        Before,
        After,
        Instead
    }

    /// <summary>
    /// 套件定義模型
    /// </summary>
    public class PackageDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _owner = string.Empty;
        private string _specification = string.Empty;
        private string _body = string.Empty;
        private DateTime _specCreated;
        private DateTime _bodyCreated;
        private string _specStatus = string.Empty;
        private string _bodyStatus = string.Empty;

        /// <summary>
        /// 套件名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 套件擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 套件規格
        /// </summary>
        public string Specification
        {
            get => _specification;
            set => SetProperty(ref _specification, value);
        }

        /// <summary>
        /// 套件主體
        /// </summary>
        public string Body
        {
            get => _body;
            set => SetProperty(ref _body, value);
        }

        /// <summary>
        /// 套件規格創建時間
        /// </summary>
        public DateTime SpecCreated
        {
            get => _specCreated;
            set => SetProperty(ref _specCreated, value);
        }

        /// <summary>
        /// 套件主體創建時間
        /// </summary>
        public DateTime BodyCreated
        {
            get => _bodyCreated;
            set => SetProperty(ref _bodyCreated, value);
        }

        /// <summary>
        /// 套件規格狀態
        /// </summary>
        public string SpecStatus
        {
            get => _specStatus;
            set => SetProperty(ref _specStatus, value);
        }

        /// <summary>
        /// 套件主體狀態
        /// </summary>
        public string BodyStatus
        {
            get => _bodyStatus;
            set => SetProperty(ref _bodyStatus, value);
        }

        /// <summary>
        /// 驗證套件定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("套件名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(Specification))
            {
                result.AddError($"套件 {Name} 的規格不能為空");
            }

            return result;
        }
    }

    /// <summary>
    /// 序列定義模型
    /// </summary>
    public class SequenceDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _owner = string.Empty;
        private long _minValue = 1;
        private long _maxValue = long.MaxValue;
        private long _incrementBy = 1;
        private long _cacheSize = 20;
        private bool _isCycling;
        private bool _isOrdered;
        private long _lastNumber = 1;

        /// <summary>
        /// 序列名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 序列擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 最小值
        /// </summary>
        public long MinValue
        {
            get => _minValue;
            set => SetProperty(ref _minValue, value);
        }

        /// <summary>
        /// 最大值
        /// </summary>
        public long MaxValue
        {
            get => _maxValue;
            set => SetProperty(ref _maxValue, value);
        }

        /// <summary>
        /// 增量
        /// </summary>
        public long IncrementBy
        {
            get => _incrementBy;
            set => SetProperty(ref _incrementBy, value);
        }

        /// <summary>
        /// 快取大小
        /// </summary>
        public long CacheSize
        {
            get => _cacheSize;
            set => SetProperty(ref _cacheSize, value);
        }

        /// <summary>
        /// 是否循環
        /// </summary>
        public bool IsCycling
        {
            get => _isCycling;
            set => SetProperty(ref _isCycling, value);
        }

        /// <summary>
        /// 是否有序
        /// </summary>
        public bool IsOrdered
        {
            get => _isOrdered;
            set => SetProperty(ref _isOrdered, value);
        }

        /// <summary>
        /// 最後產生的數字
        /// </summary>
        public long LastNumber
        {
            get => _lastNumber;
            set => SetProperty(ref _lastNumber, value);
        }

        /// <summary>
        /// 驗證序列定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("序列名稱不能為空");
            }

            if (MinValue >= MaxValue)
            {
                result.AddError($"序列 {Name} 的最小值必須小於最大值");
            }

            if (IncrementBy == 0)
            {
                result.AddError($"序列 {Name} 的增量不能為零");
            }

            if (CacheSize < 0)
            {
                result.AddError($"序列 {Name} 的快取大小不能為負數");
            }

            return result;
        }
    }

    /// <summary>
    /// 驗證結果模型
    /// </summary>
    public class ValidationResult
    {
        private List<string> _errors = new();

        /// <summary>
        /// 錯誤訊息集合
        /// </summary>
        public IReadOnlyList<string> Errors => _errors;

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid => _errors.Count == 0;

        /// <summary>
        /// 新增錯誤訊息
        /// </summary>
        /// <param name="error">錯誤訊息</param>
        public void AddError(string error)
        {
            _errors.Add(error);
        }

        /// <summary>
        /// 新增多個錯誤訊息
        /// </summary>
        /// <param name="errors">錯誤訊息集合</param>
        public void AddErrors(IEnumerable<string> errors)
        {
            _errors.AddRange(errors);
        }
    }

    /// <summary>
    /// 屬性變更通知基底類別
    /// </summary>
    public abstract class NotifyPropertyChangedBase : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 設定屬性值並觸發屬性變更事件
        /// </summary>
        /// <typeparam name="T">屬性類型</typeparam>
        /// <param name="field">欄位參考</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">屬性名稱</param>
        /// <returns>值是否已變更</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = "")
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 觸發屬性變更事件
        /// </summary>
        /// <param name="propertyName">屬性名稱</param>
        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 編譯錯誤模型
    /// </summary>
    public class CompilationError : NotifyPropertyChangedBase
    {
        private int _lineNumber;
        private int _columnNumber;
        private string _message = string.Empty;
        private string _errorCode = string.Empty;

        /// <summary>
        /// 行號
        /// </summary>
        public int LineNumber
        {
            get => _lineNumber;
            set => SetProperty(ref _lineNumber, value);
        }

        /// <summary>
        /// 欄位號
        /// </summary>
        public int ColumnNumber
        {
            get => _columnNumber;
            set => SetProperty(ref _columnNumber, value);
        }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        /// <summary>
        /// 錯誤代碼
        /// </summary>
        public string ErrorCode
        {
            get => _errorCode;
            set => SetProperty(ref _errorCode, value);
        }
    }

    /// <summary>
    /// 相依性資訊模型
    /// </summary>
    public class DependencyInfo : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _type = string.Empty;
        private string _owner = string.Empty;
        private string _dependencyType = string.Empty;
        private string _description = string.Empty;

        /// <summary>
        /// 物件名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 物件類型
        /// </summary>
        public string Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 相依性類型
        /// </summary>
        public string DependencyType
        {
            get => _dependencyType;
            set => SetProperty(ref _dependencyType, value);
        }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }
    }
}