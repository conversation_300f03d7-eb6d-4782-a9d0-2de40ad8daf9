namespace OracleMS.Models;

public class TableSchema
{
    public string TableName { get; set; } = string.Empty;
    public string Owner { get; set; } = string.Empty;
    public List<ColumnInfo> Columns { get; set; } = new();
    public List<IndexInfo> Indexes { get; set; } = new();
    public List<ConstraintInfo> Constraints { get; set; } = new();
    public List<TriggerInfo> Triggers { get; set; } = new();
}

public class ColumnInfo
{
    public string ColumnName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public int? MaxLength { get; set; }
    public int? Precision { get; set; }
    public int? Scale { get; set; }
    public bool IsNullable { get; set; }
    public string DefaultValue { get; set; } = string.Empty;
    public bool IsPrimaryKey { get; set; }
    public string Comment { get; set; } = string.Empty;
    public int Position { get; set; }
}

public class IndexInfo
{
    public string IndexName { get; set; } = string.Empty;
    public string IndexType { get; set; } = string.Empty;
    public bool IsUnique { get; set; }
    public bool IsPrimaryKey { get; set; }
    public List<string> Columns { get; set; } = new();
}

public class ConstraintInfo
{
    public string ConstraintName { get; set; } = string.Empty;
    public string ConstraintType { get; set; } = string.Empty;
    public string Columns { get; set; } = string.Empty;
    public string ReferencedTable { get; set; } = string.Empty;
    public string ReferencedColumns { get; set; } = string.Empty;
}

public class TriggerInfo
{
    public string TriggerName { get; set; } = string.Empty;
    public string TriggerType { get; set; } = string.Empty;
    public string Event { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}