﻿[2025-07-21 20:22:20.635] [INFO] [TableEditorView] TableEditorView: Loaded event fired
[2025-07-21 20:22:20.638] [INFO] [TableEditorView] TableEditorView: DataContext is set on load to TableEditorViewModel
[2025-07-21 20:22:20.638] [INFO] [TableEditorView] DataTypes property info: List`1, IsPublic=True
[2025-07-21 20:22:20.639] [INFO] [TableEditorView] DataTypes property contains 24 items
[2025-07-21 20:22:20.639] [INFO] [TableEditorView] DataTypes sample: VARCHAR2, NVARCHAR2, CH<PERSON>, NCHAR, NUMBER, ... (19 more)
[2025-07-21 20:22:20.639] [INFO] [TableEditorView] DataTypes contains all required Oracle types
[2025-07-21 20:22:20.641] [ERROR] [DataTypesFallbackConverter] DataTypes binding approach ElementName returned DependencyProperty.UnsetValue
[2025-07-21 20:22:20.642] [ERROR] [DataTypesFallbackConverter] DataTypes binding approach RelativeSource returned DependencyProperty.UnsetValue
[2025-07-21 20:22:20.643] [INFO] [DataTypesFallbackConverter] DataTypes binding succeeded using StaticResource approach with 24 items
[2025-07-21 20:22:20.644] [INFO] [TableEditorView] === AUTOMATIC BINDING TEST STARTED ===
[2025-07-21 20:22:20.644] [INFO] [TableEditorView] DataContext type: OracleMS.ViewModels.TableEditorViewModel
[2025-07-21 20:22:20.644] [INFO] [TableEditorView] DataTypes property is accessible directly with 24 items
[2025-07-21 20:22:20.644] [INFO] [TableEditorView] DataTypes content sample: VARCHAR2, NVARCHAR2, CHAR, NCHAR, NUMBER...
[2025-07-21 20:22:20.645] [INFO] [TableEditorView] Starting binding approaches test
[2025-07-21 20:22:20.645] [INFO] [TableEditorView] ElementName binding found DataTypes property with 24 items
[2025-07-21 20:22:20.646] [INFO] [TableEditorView] ElementName binding SUCCESS - Found 24 data types
[2025-07-21 20:22:20.646] [INFO] [TableEditorView] RelativeSource binding found DataTypes property with 24 items
[2025-07-21 20:22:20.646] [INFO] [TableEditorView] RelativeSource binding SUCCESS - Found 24 data types
[2025-07-21 20:22:20.647] [INFO] [TableEditorView] StaticResource binding found OracleDataTypes resource with 24 items
[2025-07-21 20:22:20.647] [INFO] [TableEditorView] StaticResource binding SUCCESS - Found 24 data types
[2025-07-21 20:22:20.648] [INFO] [TableEditorView] At least one binding approach succeeded - dropdown will work!
[2025-07-21 20:22:20.648] [INFO] [TableEditorView] === AUTOMATIC BINDING TEST COMPLETED ===
[2025-07-21 20:25:45.893] [INFO] [TableEditorView] TableEditorView: Loaded event fired
[2025-07-21 20:25:45.895] [INFO] [TableEditorView] TableEditorView: DataContext is set on load to TableEditorViewModel
[2025-07-21 20:25:45.896] [INFO] [TableEditorView] DataTypes property info: List`1, IsPublic=True
[2025-07-21 20:25:45.896] [INFO] [TableEditorView] DataTypes property contains 24 items
[2025-07-21 20:25:45.896] [INFO] [TableEditorView] DataTypes sample: VARCHAR2, NVARCHAR2, CHAR, NCHAR, NUMBER, ... (19 more)
[2025-07-21 20:25:45.897] [INFO] [TableEditorView] DataTypes contains all required Oracle types
[2025-07-21 20:25:45.899] [ERROR] [DataTypesFallbackConverter] DataTypes binding approach ElementName returned DependencyProperty.UnsetValue
[2025-07-21 20:25:45.899] [ERROR] [DataTypesFallbackConverter] DataTypes binding approach RelativeSource returned DependencyProperty.UnsetValue
[2025-07-21 20:25:45.899] [INFO] [DataTypesFallbackConverter] DataTypes binding succeeded using StaticResource approach with 24 items
[2025-07-21 20:25:45.900] [INFO] [TableEditorView] === AUTOMATIC BINDING TEST STARTED ===
[2025-07-21 20:25:45.900] [INFO] [TableEditorView] DataContext type: OracleMS.ViewModels.TableEditorViewModel
[2025-07-21 20:25:45.901] [INFO] [TableEditorView] DataTypes property is accessible directly with 24 items
[2025-07-21 20:25:45.901] [INFO] [TableEditorView] DataTypes content sample: VARCHAR2, NVARCHAR2, CHAR, NCHAR, NUMBER...
[2025-07-21 20:25:45.902] [INFO] [TableEditorView] Starting binding approaches test
[2025-07-21 20:25:45.902] [INFO] [TableEditorView] ElementName binding found DataTypes property with 24 items
[2025-07-21 20:25:45.903] [INFO] [TableEditorView] ElementName binding SUCCESS - Found 24 data types
[2025-07-21 20:25:45.903] [INFO] [TableEditorView] RelativeSource binding found DataTypes property with 24 items
[2025-07-21 20:25:45.903] [INFO] [TableEditorView] RelativeSource binding SUCCESS - Found 24 data types
[2025-07-21 20:25:45.904] [INFO] [TableEditorView] StaticResource binding found OracleDataTypes resource with 24 items
[2025-07-21 20:25:45.904] [INFO] [TableEditorView] StaticResource binding SUCCESS - Found 24 data types
[2025-07-21 20:25:45.905] [INFO] [TableEditorView] At least one binding approach succeeded - dropdown will work!
[2025-07-21 20:25:45.905] [INFO] [TableEditorView] === AUTOMATIC BINDING TEST COMPLETED ===
