2025-07-17 21:37:04.475 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 21:37:04.525 +08:00 [INF] Hosting environment: Production
2025-07-17 21:37:04.526 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-17 21:37:40.469 +08:00 [INF] 所有活動連線已關閉
2025-07-17 21:40:43.208 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 21:40:43.229 +08:00 [INF] Hosting environment: Production
2025-07-17 21:40:43.229 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-17 21:40:47.758 +08:00 [INF] 所有活動連線已關閉
2025-07-17 21:51:02.070 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 21:51:02.093 +08:00 [INF] Hosting environment: Production
2025-07-17 21:51:02.093 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-17 21:51:27.422 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 21:51:27.817 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 21:51:28.628 +08:00 [ERR] 建立 Oracle 連線時發生未預期錯誤: 新連線
System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
2025-07-17 21:51:28.965 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: 建立連線時發生錯誤: ORA-50008: 'Command Timeout' is an invalid connection string attribute
 ---> System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 65
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 21:54:21.096 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 21:54:21.124 +08:00 [INF] Hosting environment: Production
2025-07-17 21:54:21.125 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-17 21:54:47.743 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 21:54:47.769 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 21:54:48.343 +08:00 [ERR] 建立 Oracle 連線時發生未預期錯誤: 新連線
System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
2025-07-17 21:54:48.477 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: 建立連線時發生錯誤: ORA-50008: 'Command Timeout' is an invalid connection string attribute
 ---> System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 65
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 21:56:14.727 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 21:56:14.727 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 21:56:14.882 +08:00 [ERR] 建立 Oracle 連線時發生未預期錯誤: 新連線
System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
2025-07-17 21:56:14.948 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: 建立連線時發生錯誤: ORA-50008: 'Command Timeout' is an invalid connection string attribute
 ---> System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 65
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 21:56:27.218 +08:00 [INF] 所有活動連線已關閉
2025-07-17 22:02:29.327 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 22:02:29.424 +08:00 [INF] Hosting environment: Production
2025-07-17 22:02:29.425 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-17 22:04:11.563 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 22:04:11.590 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 22:05:44.511 +08:00 [ERR] 建立 Oracle 連線時發生未預期錯誤: 新連線
System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
2025-07-17 22:05:44.593 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: 建立連線時發生錯誤: ORA-50008: 'Command Timeout' is an invalid connection string attribute
 ---> System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 65
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 22:05:47.407 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 22:05:47.408 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 22:09:00.846 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.GetAsync(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.GetAsync(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.GetAsync(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString securedPassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenInternalAsync(Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsync(CancellationToken cancellationToken)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-17 22:09:00.960 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.GetAsync(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.GetAsync(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.GetAsync(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString securedPassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenInternalAsync(Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsync(CancellationToken cancellationToken)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 60
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 22:10:30.552 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 22:10:30.552 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 22:11:08.788 +08:00 [ERR] 建立 Oracle 連線時發生未預期錯誤: 新連線
System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
2025-07-17 22:11:08.846 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: 建立連線時發生錯誤: ORA-50008: 'Command Timeout' is an invalid connection string attribute
 ---> System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 65
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 22:11:20.789 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 22:11:20.789 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 22:14:17.451 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.GetAsync(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.GetAsync(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.GetAsync(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString securedPassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenInternalAsync(Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsync(CancellationToken cancellationToken)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-17 22:14:17.565 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.GetAsync(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.GetAsync(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.GetAsync(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString securedPassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenInternalAsync(Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsyncHelper(CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.OpenAsync(CancellationToken cancellationToken)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 60
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 22:14:19.592 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 22:14:19.592 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 22:14:28.168 +08:00 [ERR] 建立 Oracle 連線時發生未預期錯誤: 新連線
System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
2025-07-17 22:14:28.243 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: 建立連線時發生錯誤: ORA-50008: 'Command Timeout' is an invalid connection string attribute
 ---> System.ArgumentException: ORA-50008: 'Command Timeout' is an invalid connection string attribute
   at OracleInternal.ConnectionPool.ConnectionString.SetProperty(String key, String value, String quotedValue, String originalKey, OracleOpaqueString opaquePassw, OracleOpaqueString token)
   at OracleInternal.ConnectionPool.ConnectionString.Parse(OracleConnection con, String constr)
   at OracleInternal.ConnectionPool.ConnectionString..ctor(OracleConnection con, String constring, OracleCredential credential, OracleAccessToken accessToken, Boolean bIncludeOsUser)
   at OracleInternal.ConnectionPool.ConnectionString.GetCS(OracleConnection con, String userProvidedConStr, OracleCredential credential, OracleAccessToken accessToken, Boolean bCreateIfNotinCache, Boolean bIncludeOsUser)
   at Oracle.ManagedDataAccess.Client.OracleConnectionInternal.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection.set_ConnectionString(String value)
   at Oracle.ManagedDataAccess.Client.OracleConnection..ctor(String connectionString)
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 45
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 65
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-17 22:14:32.791 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 22:14:32.792 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 22:17:25.119 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 22:17:25.190 +08:00 [INF] Hosting environment: Production
2025-07-17 22:17:25.191 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-17 22:17:37.898 +08:00 [INF] 開始測試連線: 新連線
2025-07-17 22:17:37.932 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-17 23:02:33.221 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-17 23:02:33.343 +08:00 [INF] Hosting environment: Production
2025-07-17 23:02:33.349 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-17 23:02:38.031 +08:00 [INF] 所有活動連線已關閉
