2025-07-18 02:45:34.261 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 02:45:34.317 +08:00 [INF] Hosting environment: Production
2025-07-18 02:45:34.319 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 02:45:50.102 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 02:45:50.384 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 02:46:26.838 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, <PERSON><PERSON><PERSON> bGet<PERSON>or<PERSON><PERSON>, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForce<PERSON><PERSON>, <PERSON><PERSON>an bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-18 02:46:27.163 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 62
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-18 02:46:30.462 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 02:46:30.462 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 02:47:10.750 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-18 02:47:10.828 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 62
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-18 02:47:13.379 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 02:47:13.380 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 02:47:36.011 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-18 02:47:36.097 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 62
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-18 02:49:27.410 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 02:49:27.410 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 02:49:37.214 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50201
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50201: Oracle Communication: Failed to connect to server or failed to parse connect string
 ---> OracleInternal.Network.NetworkException (0x80004005): ORA-50201: Oracle Communication: Failed to connect to server or failed to parse connect string
 ---> OracleInternal.Network.NetworkException (0x80004005): ORA-12514: Cannot connect to database. Service AFLC is not registered with the listener at host *************/************* port 1521. (CONNECTION_ID=eOsu+gYbekyjab2lmt0lrw==)
https://docs.oracle.com/error-help/db/ora-12514/
   at OracleInternal.Network.OracleCommunication.SendConnectPacketAndProcessResponse(AddressResolution addrRes, Boolean bAsync)
   at OracleInternal.Network.OracleCommunication.ConnectViaCO(ConnectionOption connOption, AddressResolution addrRes, Boolean bAsync)
   at OracleInternal.Network.OracleCommunication.DoConnect(String tnsDescriptor, Boolean bAsync)
   at OracleInternal.Network.OracleCommunication.Connect(String tnsDescriptor, Boolean doNAHandshake, String IName, ConnectionOption CO, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionImpl.Connect(ConnectionString cs, Boolean bOpenEndUserSession, OracleConnection connRefForCriteria, String instanceName, Boolean bAsync)
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-18 02:49:37.295 +08:00 [ERR] 連線測試失敗: 新連線
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50201): ORA-50201: Oracle Communication: Failed to connect to server or failed to parse connect string
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50201: Oracle Communication: Failed to connect to server or failed to parse connect string
 ---> OracleInternal.Network.NetworkException (0x80004005): ORA-50201: Oracle Communication: Failed to connect to server or failed to parse connect string
 ---> OracleInternal.Network.NetworkException (0x80004005): ORA-12514: Cannot connect to database. Service AFLC is not registered with the listener at host *************/************* port 1521. (CONNECTION_ID=eOsu+gYbekyjab2lmt0lrw==)
https://docs.oracle.com/error-help/db/ora-12514/
   at OracleInternal.Network.OracleCommunication.SendConnectPacketAndProcessResponse(AddressResolution addrRes, Boolean bAsync)
   at OracleInternal.Network.OracleCommunication.ConnectViaCO(ConnectionOption connOption, AddressResolution addrRes, Boolean bAsync)
   at OracleInternal.Network.OracleCommunication.DoConnect(String tnsDescriptor, Boolean bAsync)
   at OracleInternal.Network.OracleCommunication.Connect(String tnsDescriptor, Boolean doNAHandshake, String IName, ConnectionOption CO, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionImpl.Connect(ConnectionString cs, Boolean bOpenEndUserSession, OracleConnection connRefForCriteria, String instanceName, Boolean bAsync)
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 62
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-18 02:51:03.162 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 02:51:03.162 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 02:51:26.710 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-18 02:51:28.478 +08:00 [INF] 連線測試成功: 新連線
2025-07-18 02:54:21.367 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 02:54:21.398 +08:00 [INF] Hosting environment: Production
2025-07-18 02:54:21.399 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 02:56:33.169 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 02:56:33.182 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 02:56:34.338 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-18 02:56:34.459 +08:00 [INF] 連線測試成功: 新連線
2025-07-18 02:57:20.151 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 02:57:20.151 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 02:57:20.169 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-18 02:57:20.201 +08:00 [INF] 連線測試成功: 新連線
2025-07-18 02:58:34.763 +08:00 [INF] 所有活動連線已關閉
2025-07-18 19:08:16.772 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 19:08:16.795 +08:00 [INF] Hosting environment: Production
2025-07-18 19:08:16.796 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 20:13:07.161 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 20:13:07.192 +08:00 [INF] Hosting environment: Production
2025-07-18 20:13:07.193 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 20:33:16.914 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 20:33:16.939 +08:00 [INF] Hosting environment: Production
2025-07-18 20:33:16.940 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 20:37:59.992 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 20:38:00.051 +08:00 [INF] Hosting environment: Production
2025-07-18 20:38:00.053 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 20:41:46.534 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 20:41:46.554 +08:00 [INF] Hosting environment: Production
2025-07-18 20:41:46.555 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 20:42:45.481 +08:00 [INF] 開始測試連線: 新連線2
2025-07-18 20:42:45.798 +08:00 [INF] 正在建立 Oracle 連線: 新連線2
2025-07-18 20:43:05.790 +08:00 [ERR] Oracle 連線失敗: 新連線2, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-18 20:43:05.956 +08:00 [ERR] 連線測試失敗: 新連線2
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 62
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-18 20:43:08.645 +08:00 [INF] 開始測試連線: 新連線2
2025-07-18 20:43:08.645 +08:00 [INF] 正在建立 Oracle 連線: 新連線2
2025-07-18 20:43:23.883 +08:00 [ERR] Oracle 連線失敗: 新連線2, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-18 20:43:23.956 +08:00 [ERR] 連線測試失敗: 新連線2
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 62
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-18 20:43:29.166 +08:00 [INF] 開始測試連線: 新連線2
2025-07-18 20:43:29.166 +08:00 [INF] 正在建立 Oracle 連線: 新連線2
2025-07-18 20:44:48.645 +08:00 [ERR] Oracle 連線失敗: 新連線2, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
2025-07-18 20:44:48.771 +08:00 [ERR] 連線測試失敗: 新連線2
OracleMS.Exceptions.OracleManagementException: Oracle 連線失敗: Oracle 錯誤 (ORA-50000): ORA-50000: Connection request timed out
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 48
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleConnectionProvider.cs:line 62
   at OracleMS.Services.ConnectionService.TestConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ConnectionService.cs:line 57
2025-07-18 20:46:21.769 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 20:46:21.817 +08:00 [INF] Hosting environment: Production
2025-07-18 20:46:21.819 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 20:46:28.389 +08:00 [INF] 開始測試連線: 新連線
2025-07-18 20:46:28.921 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-18 20:46:36.417 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-18 20:46:36.507 +08:00 [INF] 連線測試成功: 新連線
2025-07-18 20:46:52.183 +08:00 [INF] 所有活動連線已關閉
2025-07-18 20:47:36.452 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 20:47:36.492 +08:00 [INF] Hosting environment: Production
2025-07-18 20:47:36.493 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 20:52:37.352 +08:00 [INF] 所有活動連線已關閉
2025-07-18 21:21:49.215 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 21:21:49.253 +08:00 [INF] Hosting environment: Production
2025-07-18 21:21:49.254 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-18 21:23:02.388 +08:00 [INF] 所有活動連線已關閉
