2025-07-21 00:08:26.769 +08:00 [DBG] Hosting starting
2025-07-21 00:08:26.813 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 00:08:26.815 +08:00 [INF] Hosting environment: Production
2025-07-21 00:08:26.816 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 00:08:26.816 +08:00 [DBG] Hosting started
2025-07-21 00:08:30.306 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:08:30.461 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:08:31.421 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 00:08:31.423 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 00:08:33.231 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 00:08:33.419 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 00:08:33.420 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:08:33.643 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:08:33.643 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 00:08:33.680 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 00:08:33.680 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 00:08:33.680 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 00:08:37.724 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:08:37.726 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:08:37.729 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 00:08:37.827 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 00:08:37.829 +08:00 [INF] 成功取得 5 個 "Package" 物件，耗時 104 毫秒
2025-07-21 00:08:42.260 +08:00 [INF] 開始載入套件定義: PKTEST
2025-07-21 00:08:42.262 +08:00 [INF] 正在取得套件定義: PKTEST
2025-07-21 00:08:42.909 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE
2025-07-21 00:08:42.916 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE
2025-07-21 00:08:42.954 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE, 行數: 5, 內容長度: 120
2025-07-21 00:08:42.954 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE BODY
2025-07-21 00:08:42.954 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE BODY
2025-07-21 00:08:42.962 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE BODY, 行數: 7, 內容長度: 163
2025-07-21 00:08:43.224 +08:00 [INF] 成功取得套件定義: PKTEST
2025-07-21 00:08:43.224 +08:00 [INF] 從服務取得套件定義: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 00:08:43.224 +08:00 [INF] PackageDefinition 屬性已設置: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 00:08:43.225 +08:00 [INF] 開始設置文件內容，_isInitializing = true
2025-07-21 00:08:43.225 +08:00 [INF] 準備設置文件內容 - 規格長度: 120, 主體長度: 163
2025-07-21 00:08:55.191 +08:00 [DBG] 視窗設定已儲存
2025-07-21 00:08:55.192 +08:00 [INF] 所有活動連線已關閉
2025-07-21 00:09:43.688 +08:00 [DBG] Hosting starting
2025-07-21 00:09:43.720 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 00:09:43.723 +08:00 [INF] Hosting environment: Production
2025-07-21 00:09:43.723 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 00:09:43.723 +08:00 [DBG] Hosting started
2025-07-21 00:09:46.707 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:09:46.875 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:09:47.929 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 00:09:47.930 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 00:09:49.900 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 00:09:49.927 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 00:09:49.927 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:09:50.144 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:09:50.145 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 00:09:50.160 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 00:09:50.160 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 00:09:50.161 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 00:09:53.017 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:09:53.019 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:09:53.023 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 00:09:53.147 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 00:09:53.148 +08:00 [INF] 成功取得 5 個 "Package" 物件，耗時 130 毫秒
2025-07-21 00:09:57.756 +08:00 [INF] 開始載入套件定義: TO_EIS
2025-07-21 00:09:57.757 +08:00 [INF] 正在取得套件定義: TO_EIS
2025-07-21 00:09:59.874 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE
2025-07-21 00:09:59.880 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE
2025-07-21 00:09:59.918 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE, 行數: 72, 內容長度: 1688
2025-07-21 00:09:59.919 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE BODY
2025-07-21 00:09:59.919 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE BODY
2025-07-21 00:09:59.969 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE BODY, 行數: 313, 內容長度: 10149
2025-07-21 00:09:59.984 +08:00 [INF] 成功取得套件定義: TO_EIS
2025-07-21 00:09:59.984 +08:00 [INF] 從服務取得套件定義: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 00:09:59.984 +08:00 [INF] PackageDefinition 屬性已設置: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 00:09:59.985 +08:00 [INF] 開始設置文件內容，_isInitializing = true
2025-07-21 00:09:59.985 +08:00 [INF] 準備設置文件內容 - 規格長度: 1688, 主體長度: 10149
2025-07-21 00:19:29.502 +08:00 [DBG] 視窗設定已儲存
2025-07-21 00:19:29.503 +08:00 [INF] 所有活動連線已關閉
2025-07-21 00:19:45.926 +08:00 [DBG] Hosting starting
2025-07-21 00:19:45.971 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 00:19:45.977 +08:00 [INF] Hosting environment: Production
2025-07-21 00:19:45.979 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 00:19:45.979 +08:00 [DBG] Hosting started
2025-07-21 00:19:48.255 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:19:48.392 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:19:49.574 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 00:19:49.575 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 00:19:51.266 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 00:19:51.370 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 00:19:51.370 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:19:51.543 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:19:51.544 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 00:19:51.560 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 00:19:51.560 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 00:19:51.560 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 00:19:54.148 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:19:54.149 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:19:54.152 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 00:19:54.257 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 00:19:54.258 +08:00 [INF] 成功取得 5 個 "Package" 物件，耗時 110 毫秒
2025-07-21 00:20:13.992 +08:00 [INF] 開始載入套件定義: PKTEST
2025-07-21 00:20:20.924 +08:00 [INF] 正在取得套件定義: PKTEST
2025-07-21 00:20:30.602 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE
2025-07-21 00:20:43.721 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE
2025-07-21 00:20:47.584 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE, 行數: 5, 內容長度: 120
2025-07-21 00:20:51.350 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE BODY
2025-07-21 00:20:51.350 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE BODY
2025-07-21 00:20:51.372 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE BODY, 行數: 7, 內容長度: 163
2025-07-21 00:20:54.454 +08:00 [INF] 成功取得套件定義: PKTEST
2025-07-21 00:21:00.302 +08:00 [INF] 從服務取得套件定義: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 00:21:05.201 +08:00 [INF] PackageDefinition 屬性已設置: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 00:21:08.316 +08:00 [INF] 開始設置文件內容，_isInitializing = true
2025-07-21 00:21:15.226 +08:00 [INF] 準備設置文件內容 - 規格長度: 120, 主體長度: 163
2025-07-21 00:25:17.586 +08:00 [DBG] 視窗設定已儲存
2025-07-21 00:25:17.589 +08:00 [INF] 所有活動連線已關閉
2025-07-21 00:56:33.920 +08:00 [DBG] Hosting starting
2025-07-21 00:56:33.951 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 00:56:33.954 +08:00 [INF] Hosting environment: Production
2025-07-21 00:56:33.955 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 00:56:33.955 +08:00 [DBG] Hosting started
2025-07-21 00:57:02.170 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:57:02.359 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:57:03.278 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 00:57:03.280 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 00:57:05.165 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 00:57:05.202 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 00:57:05.202 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:57:05.396 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:57:05.396 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 00:57:05.419 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 00:57:05.419 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 00:57:05.419 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 00:57:15.002 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:57:15.003 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 00:57:15.006 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 00:57:15.105 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 00:57:15.108 +08:00 [INF] 成功取得 5 個 "Package" 物件，耗時 105 毫秒
2025-07-21 00:57:18.745 +08:00 [INF] 開始載入套件定義: PKTEST
2025-07-21 00:57:18.746 +08:00 [INF] 正在取得套件定義: PKTEST
2025-07-21 00:57:19.358 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE
2025-07-21 00:57:19.361 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE
2025-07-21 00:57:19.389 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE, 行數: 5, 內容長度: 120
2025-07-21 00:57:19.389 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE BODY
2025-07-21 00:57:19.389 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE BODY
2025-07-21 00:57:19.394 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE BODY, 行數: 7, 內容長度: 163
2025-07-21 00:57:19.893 +08:00 [INF] 成功取得套件定義: PKTEST
2025-07-21 00:57:19.894 +08:00 [INF] 從服務取得套件定義: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 00:57:19.894 +08:00 [INF] PackageDefinition 屬性已設置: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 00:57:19.894 +08:00 [INF] 開始設置文字內容，_isInitializing = true
2025-07-21 00:57:19.894 +08:00 [INF] 準備設置文字內容 - 規格長度: 120, 主體長度: 163
2025-07-21 00:57:19.894 +08:00 [INF] 規格文字內容已設置，實際長度: 120
2025-07-21 00:57:19.894 +08:00 [INF] 主體文字內容已設置，實際長度: 163
2025-07-21 00:57:19.894 +08:00 [INF] 文字內容設置完成，_isInitializing = false
2025-07-21 00:57:19.897 +08:00 [INF] 正在執行 SQL 語句，長度: 1120
2025-07-21 00:57:19.898 +08:00 [INF] 正在執行查詢，SQL 長度: 1120
2025-07-21 00:57:19.898 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 00:57:20.002 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 00:57:20.002 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 104 毫秒
2025-07-21 00:59:02.008 +08:00 [DBG] 視窗設定已儲存
2025-07-21 00:59:02.009 +08:00 [INF] 所有活動連線已關閉
2025-07-21 00:59:27.277 +08:00 [DBG] Hosting starting
2025-07-21 00:59:27.317 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 00:59:27.320 +08:00 [INF] Hosting environment: Production
2025-07-21 00:59:27.321 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 00:59:27.322 +08:00 [DBG] Hosting started
2025-07-21 00:59:30.223 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:59:30.360 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:59:31.360 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 00:59:31.361 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 00:59:33.129 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 00:59:33.155 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 00:59:33.155 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 00:59:33.335 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 00:59:33.336 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 00:59:33.349 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 00:59:33.349 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 00:59:33.349 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 00:59:36.441 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 00:59:36.443 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 00:59:36.445 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 00:59:36.540 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 00:59:36.541 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 99 毫秒
2025-07-21 00:59:38.143 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 00:59:42.883 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 00:59:42.886 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:01:06.887 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 01:01:09.005 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 01:01:09.011 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:01:30.382 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:01:34.210 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:02:58.669 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:02:58.669 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:02:58.674 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:02:58.678 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:02:58.678 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:02:58.794 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:02:58.795 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 116 毫秒
2025-07-21 01:02:58.799 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:02:58.800 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:02:58.800 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:02:58.812 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:02:58.813 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 13 毫秒
2025-07-21 01:03:12.571 +08:00 [INF] 正在取得預存程序原始碼: TOTM_A
2025-07-21 01:03:13.086 +08:00 [INF] 正在取得物件原始碼: TOTM_A, 類型: PROCEDURE
2025-07-21 01:03:13.086 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TOTM_A, TYPE=PROCEDURE
2025-07-21 01:03:14.350 +08:00 [INF] 正在取得預存程序原始碼: TOTM_A
2025-07-21 01:03:15.569 +08:00 [INF] 正在取得物件原始碼: TOTM_A, 類型: PROCEDURE
2025-07-21 01:03:15.569 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TOTM_A, TYPE=PROCEDURE
2025-07-21 01:03:15.587 +08:00 [INF] 成功取得物件原始碼: TOTM_A, 類型: PROCEDURE, 行數: 25, 內容長度: 412
2025-07-21 01:03:15.588 +08:00 [INF] 成功取得預存程序原始碼: TOTM_A
2025-07-21 01:03:15.647 +08:00 [INF] 成功取得物件原始碼: TOTM_A, 類型: PROCEDURE, 行數: 25, 內容長度: 412
2025-07-21 01:03:15.647 +08:00 [INF] 成功取得預存程序原始碼: TOTM_A
2025-07-21 01:03:15.648 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:03:15.648 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:03:15.648 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:03:15.729 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:03:15.729 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 81 毫秒
2025-07-21 01:03:15.729 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:03:15.729 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:03:15.729 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:03:16.122 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 01:03:16.122 +08:00 [INF] 查詢執行成功，返回 5 筆資料，耗時 392 毫秒
2025-07-21 01:03:27.842 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:03:27.843 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:06:45.530 +08:00 [DBG] Hosting starting
2025-07-21 01:06:45.582 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:06:45.587 +08:00 [INF] Hosting environment: Production
2025-07-21 01:06:45.588 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:06:45.589 +08:00 [DBG] Hosting started
2025-07-21 01:06:48.430 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:06:48.598 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:06:49.608 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:06:49.608 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:06:55.147 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:06:55.195 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:06:55.195 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:06:55.425 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:06:55.426 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:06:55.443 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:06:55.443 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:06:55.444 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:06:59.756 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:06:59.758 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:06:59.761 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:07:24.136 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:07:24.138 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:07:44.120 +08:00 [DBG] Hosting starting
2025-07-21 01:07:44.156 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:07:44.158 +08:00 [INF] Hosting environment: Production
2025-07-21 01:07:44.159 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:07:44.159 +08:00 [DBG] Hosting started
2025-07-21 01:07:47.738 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:07:47.888 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:07:48.806 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:07:48.808 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:07:51.441 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:07:51.469 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:07:51.469 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:07:51.680 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:07:51.681 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:07:51.705 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:07:51.705 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:07:51.706 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:07:55.095 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 01:07:55.096 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 01:07:55.099 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 01:07:55.539 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 01:07:55.542 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 446 毫秒
2025-07-21 01:07:57.724 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:07:57.724 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:07:57.725 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:08:00.162 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 01:08:00.163 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 2438 毫秒
2025-07-21 01:08:02.081 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 01:08:02.081 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 01:08:02.082 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 01:08:02.182 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 01:08:02.183 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 102 毫秒
2025-07-21 01:08:51.906 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 01:08:51.906 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 01:08:51.907 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 01:08:52.635 +08:00 [INF] 查詢執行成功，返回 226 筆資料
2025-07-21 01:08:52.637 +08:00 [INF] 成功取得 226 個 "Index" 物件，耗時 731 毫秒
2025-07-21 01:08:53.886 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 01:08:53.886 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 01:08:53.886 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 01:08:54.031 +08:00 [INF] 查詢執行成功，返回 55 筆資料
2025-07-21 01:08:54.034 +08:00 [INF] 成功取得 55 個 "Trigger" 物件，耗時 148 毫秒
2025-07-21 01:09:33.050 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 01:09:33.724 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 01:09:33.734 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:09:35.316 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 01:09:36.545 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 01:09:36.545 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:09:36.567 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:09:36.568 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:09:36.588 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:09:36.590 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:09:36.591 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:09:36.591 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:09:36.595 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:09:36.595 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:09:36.595 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:09:36.595 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:09:37.577 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:09:37.577 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 982 毫秒
2025-07-21 01:09:37.580 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:09:37.637 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:09:37.637 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:09:37.638 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:09:37.638 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 1043 毫秒
2025-07-21 01:09:37.696 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:09:38.241 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:09:38.241 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:09:38.243 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:09:38.243 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 606 毫秒
2025-07-21 01:09:38.261 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:09:38.261 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 19 毫秒
2025-07-21 01:09:42.898 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 01:09:42.898 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 01:09:42.898 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:09:43.501 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 01:09:43.502 +08:00 [INF] 成功取得 5 個 "Package" 物件，耗時 604 毫秒
2025-07-21 01:09:48.233 +08:00 [INF] 開始載入套件定義: PKTEST
2025-07-21 01:09:48.236 +08:00 [INF] 正在取得套件定義: PKTEST
2025-07-21 01:09:48.829 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE
2025-07-21 01:09:48.830 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE
2025-07-21 01:09:48.858 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE, 行數: 5, 內容長度: 120
2025-07-21 01:09:48.858 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE BODY
2025-07-21 01:09:48.858 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE BODY
2025-07-21 01:09:50.095 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE BODY, 行數: 7, 內容長度: 163
2025-07-21 01:10:08.131 +08:00 [INF] 成功取得套件定義: PKTEST
2025-07-21 01:10:08.131 +08:00 [INF] 從服務取得套件定義: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 01:10:08.131 +08:00 [INF] PackageDefinition 屬性已設置: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 01:10:08.131 +08:00 [INF] 開始設置文字內容，_isInitializing = true
2025-07-21 01:10:08.131 +08:00 [INF] 準備設置文字內容 - 規格長度: 120, 主體長度: 163
2025-07-21 01:10:08.131 +08:00 [INF] 規格文字內容已設置，實際長度: 120
2025-07-21 01:10:08.131 +08:00 [INF] 主體文字內容已設置，實際長度: 163
2025-07-21 01:10:08.131 +08:00 [INF] 文字內容設置完成，_isInitializing = false
2025-07-21 01:10:08.134 +08:00 [INF] 正在執行 SQL 語句，長度: 1120
2025-07-21 01:10:08.134 +08:00 [INF] 正在執行查詢，SQL 長度: 1120
2025-07-21 01:10:08.135 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:10:08.146 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:10:08.146 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 11 毫秒
2025-07-21 01:10:14.330 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:10:14.330 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:10:14.330 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:10:14.369 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:10:14.370 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 39 毫秒
2025-07-21 01:10:18.059 +08:00 [INF] 正在取得函數原始碼: GET_CODELEVEL
2025-07-21 01:10:18.059 +08:00 [INF] 正在取得物件原始碼: GET_CODELEVEL, 類型: FUNCTION
2025-07-21 01:10:18.059 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_CODELEVEL, TYPE=FUNCTION
2025-07-21 01:10:19.247 +08:00 [INF] 正在取得函數原始碼: GET_CODELEVEL
2025-07-21 01:10:19.247 +08:00 [INF] 正在取得物件原始碼: GET_CODELEVEL, 類型: FUNCTION
2025-07-21 01:10:19.247 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_CODELEVEL, TYPE=FUNCTION
2025-07-21 01:10:19.804 +08:00 [INF] 成功取得物件原始碼: GET_CODELEVEL, 類型: FUNCTION, 行數: 12, 內容長度: 267
2025-07-21 01:10:19.805 +08:00 [INF] 成功取得函數原始碼: GET_CODELEVEL
2025-07-21 01:10:19.859 +08:00 [INF] 成功取得物件原始碼: GET_CODELEVEL, 類型: FUNCTION, 行數: 12, 內容長度: 267
2025-07-21 01:10:19.860 +08:00 [INF] 成功取得函數原始碼: GET_CODELEVEL
2025-07-21 01:10:19.861 +08:00 [INF] 正在執行 SQL 語句，長度: 726
2025-07-21 01:10:19.861 +08:00 [INF] 正在執行查詢，SQL 長度: 726
2025-07-21 01:10:19.861 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:10:19.940 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:10:19.940 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 79 毫秒
2025-07-21 01:10:19.945 +08:00 [INF] 正在執行 SQL 語句，長度: 1138
2025-07-21 01:10:19.945 +08:00 [INF] 正在執行查詢，SQL 長度: 1138
2025-07-21 01:10:19.945 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:10:20.030 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:10:20.030 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 85 毫秒
2025-07-21 01:10:35.669 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:10:35.669 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:10:35.669 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:10:36.835 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:10:36.836 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:10:36.836 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:10:37.432 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:10:37.432 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:10:37.433 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:10:37.969 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:10:37.969 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:10:37.972 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:10:37.974 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:10:37.974 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:10:37.999 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:10:37.999 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:10:38.072 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:10:38.072 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 103 毫秒
2025-07-21 01:10:38.073 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:10:38.096 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:10:38.175 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:10:38.175 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 176 毫秒
2025-07-21 01:10:38.176 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:10:38.206 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:10:38.571 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:10:38.571 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:10:38.574 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:10:38.574 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 398 毫秒
2025-07-21 01:10:38.664 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:10:38.664 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 92 毫秒
2025-07-21 01:10:46.942 +08:00 [INF] 正在取得函數原始碼: TO_COMB_TONO
2025-07-21 01:10:46.942 +08:00 [INF] 正在取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION
2025-07-21 01:10:46.942 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_COMB_TONO, TYPE=FUNCTION
2025-07-21 01:10:48.100 +08:00 [INF] 正在取得函數原始碼: TO_COMB_TONO
2025-07-21 01:10:48.692 +08:00 [INF] 正在取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION
2025-07-21 01:10:48.693 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_COMB_TONO, TYPE=FUNCTION
2025-07-21 01:10:48.704 +08:00 [INF] 成功取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION, 行數: 16, 內容長度: 225
2025-07-21 01:10:48.704 +08:00 [INF] 成功取得函數原始碼: TO_COMB_TONO
2025-07-21 01:10:48.705 +08:00 [INF] 正在執行 SQL 語句，長度: 725
2025-07-21 01:10:48.725 +08:00 [INF] 正在執行查詢，SQL 長度: 725
2025-07-21 01:10:48.725 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:10:48.726 +08:00 [INF] 成功取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION, 行數: 16, 內容長度: 225
2025-07-21 01:10:48.726 +08:00 [INF] 成功取得函數原始碼: TO_COMB_TONO
2025-07-21 01:10:48.726 +08:00 [INF] 正在執行 SQL 語句，長度: 725
2025-07-21 01:10:48.774 +08:00 [INF] 正在執行查詢，SQL 長度: 725
2025-07-21 01:10:48.775 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:10:48.864 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:10:48.864 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 139 毫秒
2025-07-21 01:10:48.865 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 01:10:48.943 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 01:10:48.943 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:10:48.943 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:10:48.943 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 168 毫秒
2025-07-21 01:10:48.966 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 01:10:49.605 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 01:10:49.605 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:10:49.606 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:10:49.607 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 663 毫秒
2025-07-21 01:10:49.709 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:10:49.709 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 104 毫秒
2025-07-21 01:10:55.497 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:10:55.497 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:10:55.497 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:10:56.437 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:10:56.438 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:10:56.438 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:10:56.960 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:10:56.960 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:10:56.997 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:10:56.997 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:10:56.997 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 01:10:56.997 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 01:10:56.997 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:10:57.389 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:10:57.389 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 391 毫秒
2025-07-21 01:10:57.392 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 01:10:57.392 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 01:10:57.392 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:10:57.489 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:10:57.489 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 96 毫秒
2025-07-21 01:12:20.458 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 01:12:20.458 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 01:12:20.458 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 01:12:21.516 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 01:12:21.517 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 01:12:21.517 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 01:12:22.104 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 01:12:22.104 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 01:12:22.104 +08:00 [INF] 正在執行 SQL 語句，長度: 721
2025-07-21 01:12:23.325 +08:00 [INF] 正在執行查詢，SQL 長度: 721
2025-07-21 01:12:23.325 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:12:23.327 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 01:12:23.327 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 01:12:23.329 +08:00 [INF] 正在執行 SQL 語句，長度: 721
2025-07-21 01:12:23.334 +08:00 [INF] 正在執行查詢，SQL 長度: 721
2025-07-21 01:12:23.335 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:12:23.424 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:12:23.424 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 98 毫秒
2025-07-21 01:12:23.424 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:12:23.450 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:12:23.596 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:12:23.596 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:12:23.596 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 261 毫秒
2025-07-21 01:12:23.624 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:12:23.659 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:12:23.660 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:12:23.660 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:12:23.661 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 65 毫秒
2025-07-21 01:12:23.716 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:12:23.716 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 56 毫秒
2025-07-21 01:12:58.875 +08:00 [INF] 正在取得函數原始碼: TOOL_SUNDATE
2025-07-21 01:12:58.875 +08:00 [INF] 正在取得物件原始碼: TOOL_SUNDATE, 類型: FUNCTION
2025-07-21 01:12:58.875 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TOOL_SUNDATE, TYPE=FUNCTION
2025-07-21 01:12:59.983 +08:00 [INF] 正在取得函數原始碼: TOOL_SUNDATE
2025-07-21 01:12:59.983 +08:00 [INF] 正在取得物件原始碼: TOOL_SUNDATE, 類型: FUNCTION
2025-07-21 01:12:59.984 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TOOL_SUNDATE, TYPE=FUNCTION
2025-07-21 01:13:00.640 +08:00 [INF] 成功取得物件原始碼: TOOL_SUNDATE, 類型: FUNCTION, 行數: 21, 內容長度: 268
2025-07-21 01:13:00.640 +08:00 [INF] 成功取得函數原始碼: TOOL_SUNDATE
2025-07-21 01:13:00.640 +08:00 [INF] 正在執行 SQL 語句，長度: 725
2025-07-21 01:13:00.645 +08:00 [INF] 成功取得物件原始碼: TOOL_SUNDATE, 類型: FUNCTION, 行數: 21, 內容長度: 268
2025-07-21 01:13:00.645 +08:00 [INF] 成功取得函數原始碼: TOOL_SUNDATE
2025-07-21 01:13:00.645 +08:00 [INF] 正在執行 SQL 語句，長度: 725
2025-07-21 01:13:00.645 +08:00 [INF] 正在執行查詢，SQL 長度: 725
2025-07-21 01:13:00.645 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:13:00.659 +08:00 [INF] 正在執行查詢，SQL 長度: 725
2025-07-21 01:13:00.659 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:13:00.737 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:13:00.737 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 92 毫秒
2025-07-21 01:13:00.758 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 01:13:00.822 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 01:13:00.822 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:13:00.823 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:13:00.824 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 165 毫秒
2025-07-21 01:13:00.825 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 01:13:00.875 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 01:13:00.875 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:13:00.876 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:13:00.876 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 54 毫秒
2025-07-21 01:13:00.930 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:13:00.930 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 54 毫秒
2025-07-21 01:13:16.919 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:13:16.919 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:13:16.919 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:13:18.013 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:13:18.013 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:13:18.013 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:13:18.614 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:13:18.614 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:13:18.614 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:13:22.699 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:13:22.699 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:13:22.701 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:13:22.702 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:13:22.702 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:13:22.711 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:13:23.027 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:13:23.028 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:13:23.028 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 328 毫秒
2025-07-21 01:13:23.029 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:13:23.032 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:13:23.112 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:13:23.112 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:13:23.113 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 85 毫秒
2025-07-21 01:13:23.165 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:13:23.181 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:13:23.181 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:13:23.182 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:13:23.182 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 70 毫秒
2025-07-21 01:13:23.236 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:13:23.236 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 55 毫秒
2025-07-21 01:13:33.490 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:13:33.493 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:13:48.782 +08:00 [DBG] Hosting starting
2025-07-21 01:13:48.829 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:13:48.834 +08:00 [INF] Hosting environment: Production
2025-07-21 01:13:48.834 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:13:48.835 +08:00 [DBG] Hosting started
2025-07-21 01:13:51.444 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:13:51.652 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:13:52.395 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:13:52.397 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:13:54.247 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:13:54.260 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:13:54.260 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:13:54.459 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:13:54.460 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:13:54.475 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:13:54.475 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:13:54.475 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:14:02.198 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:14:02.200 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:14:02.203 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:14:02.278 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:14:02.279 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 80 毫秒
2025-07-21 01:14:04.180 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:14:04.181 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:14:04.183 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:14:04.203 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:14:04.225 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:14:04.226 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:14:04.239 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:14:04.239 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:14:04.244 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:14:04.244 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:14:04.245 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:14:04.245 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:14:04.246 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:14:04.246 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:14:04.246 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:14:04.246 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:14:04.366 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:14:04.366 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 119 毫秒
2025-07-21 01:14:04.381 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:14:04.430 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:14:04.430 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:14:04.430 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:14:04.431 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 184 毫秒
2025-07-21 01:14:04.434 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:14:04.548 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:14:04.549 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:14:04.550 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:14:04.550 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 119 毫秒
2025-07-21 01:14:04.610 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:14:04.610 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 61 毫秒
2025-07-21 01:15:02.380 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:15:02.381 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:15:02.381 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:15:02.395 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:15:02.395 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:15:02.395 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:15:02.400 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:15:02.400 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:15:02.401 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:15:02.403 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:15:02.403 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 01:15:02.403 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 01:15:02.403 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:15:02.484 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:15:02.484 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 81 毫秒
2025-07-21 01:15:02.485 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 01:15:02.485 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 01:15:02.485 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:15:02.543 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:15:02.543 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 58 毫秒
2025-07-21 01:15:24.087 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:15:24.088 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:19:54.922 +08:00 [DBG] Hosting starting
2025-07-21 01:19:54.947 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:19:54.949 +08:00 [INF] Hosting environment: Production
2025-07-21 01:19:54.949 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:19:54.950 +08:00 [DBG] Hosting started
2025-07-21 01:20:19.419 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:20:19.609 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:20:21.337 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:20:21.338 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:20:23.209 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:20:23.236 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:20:23.236 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:20:23.435 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:20:23.436 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:20:23.448 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:20:23.448 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:20:23.448 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:20:27.282 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:20:27.283 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:20:27.285 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:20:27.414 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:20:27.415 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 133 毫秒
2025-07-21 01:20:32.525 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 01:20:32.526 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 01:20:32.529 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 01:20:33.955 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 01:20:33.956 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 01:20:33.956 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 01:20:34.773 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 01:20:37.715 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 01:20:46.511 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 01:20:47.580 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 01:20:47.593 +08:00 [INF] 正在執行 SQL 語句，長度: 721
2025-07-21 01:20:47.598 +08:00 [INF] 正在執行查詢，SQL 長度: 721
2025-07-21 01:20:47.599 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:20:47.717 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:20:47.717 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 118 毫秒
2025-07-21 01:20:47.725 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:20:47.725 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:20:47.725 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:20:47.788 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:20:47.788 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 63 毫秒
2025-07-21 01:20:57.614 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:20:57.614 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:20:57.614 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:20:59.016 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:20:59.017 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:20:59.017 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:21:00.031 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:21:01.491 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:06.906 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:21:10.055 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:15.278 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:21:15.278 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:21:15.278 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:21:15.414 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:21:15.414 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 135 毫秒
2025-07-21 01:21:15.415 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:21:15.416 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:21:15.416 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:21:15.472 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:21:15.472 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 56 毫秒
2025-07-21 01:21:29.914 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:29.914 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:21:29.914 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:21:32.258 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:34.287 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:21:34.289 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:21:34.322 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:21:39.450 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:39.455 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:21:39.458 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:39.459 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:21:39.459 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:21:39.461 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:21:39.483 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:21:39.593 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:21:39.593 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:21:39.593 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:21:39.593 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 132 毫秒
2025-07-21 01:21:39.639 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:21:39.694 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:21:39.695 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:21:39.695 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:21:39.696 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 102 毫秒
2025-07-21 01:21:39.697 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:21:39.747 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:21:39.747 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:21:39.747 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:21:39.749 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 53 毫秒
2025-07-21 01:21:39.817 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:21:39.817 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 70 毫秒
2025-07-21 01:21:56.861 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:56.861 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:21:56.861 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:21:57.833 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:21:57.833 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:21:57.833 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:21:58.756 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:22:00.288 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:22:00.390 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:22:01.136 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:22:01.137 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:22:01.137 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:22:01.137 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:22:01.272 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:22:01.272 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 134 毫秒
2025-07-21 01:22:01.272 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:22:01.273 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:22:01.273 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:22:01.323 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:22:01.324 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 51 毫秒
2025-07-21 01:23:13.885 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:13.886 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:23:13.886 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:23:14.264 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:14.264 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:23:14.264 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:23:14.471 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:23:14.708 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:14.757 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:23:14.927 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:14.928 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:23:14.928 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:23:14.928 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:23:15.040 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:23:15.040 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 111 毫秒
2025-07-21 01:23:15.040 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:23:15.040 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:23:15.040 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:23:15.092 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:23:15.093 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 52 毫秒
2025-07-21 01:23:30.677 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:30.677 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:23:30.677 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:23:31.149 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:31.640 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:23:31.642 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:23:31.651 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:23:31.651 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:31.651 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:23:31.656 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:23:31.656 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:23:31.658 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:23:31.658 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:23:31.658 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:23:31.661 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:23:31.728 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:23:31.728 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:23:31.728 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 72 毫秒
2025-07-21 01:23:31.728 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:23:31.732 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:23:31.732 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:23:31.796 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:23:31.796 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 68 毫秒
2025-07-21 01:23:31.825 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:23:31.852 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:23:31.852 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:23:31.853 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:23:31.853 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 121 毫秒
2025-07-21 01:23:31.919 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:23:31.919 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 67 毫秒
2025-07-21 01:23:40.788 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:23:40.788 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:23:40.788 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:23:41.180 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:23:41.180 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:23:41.180 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:23:41.382 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:23:41.383 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:23:41.416 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:23:41.416 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:23:41.417 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 01:23:41.418 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 01:23:41.418 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:23:41.495 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:23:41.495 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 77 毫秒
2025-07-21 01:23:41.497 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 01:23:41.497 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 01:23:41.497 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:23:41.558 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:23:41.558 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 61 毫秒
2025-07-21 01:26:39.028 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:26:39.028 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:26:39.028 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:26:39.844 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:26:39.845 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:26:39.845 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:26:40.380 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:26:40.383 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:26:40.452 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:26:40.452 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:26:40.453 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 01:26:40.453 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 01:26:40.453 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:26:40.546 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:26:40.548 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 94 毫秒
2025-07-21 01:26:40.550 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 01:26:40.551 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 01:26:40.551 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:26:40.607 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:26:40.607 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 56 毫秒
2025-07-21 01:26:47.349 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:26:47.349 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:26:47.350 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:26:48.197 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:26:48.197 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:26:48.197 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:26:48.763 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:26:48.763 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:26:48.811 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:26:48.812 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:26:48.812 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:26:48.812 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:26:48.812 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:26:48.892 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:26:48.892 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 80 毫秒
2025-07-21 01:26:48.892 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:26:48.892 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:26:48.892 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:26:48.941 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:26:48.941 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 49 毫秒
2025-07-21 01:26:58.104 +08:00 [INF] 正在取得函數原始碼: TO_COMB_TONO
2025-07-21 01:26:58.104 +08:00 [INF] 正在取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION
2025-07-21 01:26:58.104 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_COMB_TONO, TYPE=FUNCTION
2025-07-21 01:26:59.243 +08:00 [INF] 正在取得函數原始碼: TO_COMB_TONO
2025-07-21 01:26:59.244 +08:00 [INF] 正在取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION
2025-07-21 01:26:59.244 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_COMB_TONO, TYPE=FUNCTION
2025-07-21 01:26:59.254 +08:00 [INF] 成功取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION, 行數: 16, 內容長度: 225
2025-07-21 01:26:59.254 +08:00 [INF] 成功取得函數原始碼: TO_COMB_TONO
2025-07-21 01:26:59.303 +08:00 [INF] 成功取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION, 行數: 16, 內容長度: 225
2025-07-21 01:26:59.303 +08:00 [INF] 成功取得函數原始碼: TO_COMB_TONO
2025-07-21 01:26:59.303 +08:00 [INF] 正在執行 SQL 語句，長度: 725
2025-07-21 01:26:59.303 +08:00 [INF] 正在執行查詢，SQL 長度: 725
2025-07-21 01:26:59.303 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:26:59.417 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:26:59.418 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 114 毫秒
2025-07-21 01:26:59.418 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 01:26:59.418 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 01:26:59.418 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:26:59.481 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:26:59.482 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 63 毫秒
2025-07-21 01:27:41.078 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:27:41.078 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:27:41.078 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:27:41.941 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:27:41.941 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:27:41.941 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:27:42.496 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:27:42.496 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:27:42.559 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:27:42.559 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:27:42.559 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:27:42.559 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:27:42.559 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:27:42.669 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:27:42.670 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 110 毫秒
2025-07-21 01:27:42.671 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:27:42.672 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:27:42.672 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:27:42.732 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:27:42.732 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 60 毫秒
2025-07-21 01:27:58.012 +08:00 [INF] 正在取得函數原始碼: TO_COMB_TONO
2025-07-21 01:27:58.012 +08:00 [INF] 正在取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION
2025-07-21 01:27:58.012 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_COMB_TONO, TYPE=FUNCTION
2025-07-21 01:27:58.801 +08:00 [INF] 正在取得函數原始碼: TO_COMB_TONO
2025-07-21 01:27:58.801 +08:00 [INF] 正在取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION
2025-07-21 01:27:58.801 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_COMB_TONO, TYPE=FUNCTION
2025-07-21 01:27:59.247 +08:00 [INF] 成功取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION, 行數: 16, 內容長度: 225
2025-07-21 01:27:59.249 +08:00 [INF] 成功取得函數原始碼: TO_COMB_TONO
2025-07-21 01:27:59.293 +08:00 [INF] 成功取得物件原始碼: TO_COMB_TONO, 類型: FUNCTION, 行數: 16, 內容長度: 225
2025-07-21 01:27:59.294 +08:00 [INF] 成功取得函數原始碼: TO_COMB_TONO
2025-07-21 01:27:59.294 +08:00 [INF] 正在執行 SQL 語句，長度: 725
2025-07-21 01:27:59.294 +08:00 [INF] 正在執行查詢，SQL 長度: 725
2025-07-21 01:27:59.294 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:27:59.436 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:27:59.437 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 143 毫秒
2025-07-21 01:27:59.438 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 01:27:59.439 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 01:27:59.439 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:27:59.493 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:27:59.493 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 54 毫秒
2025-07-21 01:28:05.762 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:28:05.763 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:28:16.725 +08:00 [DBG] Hosting starting
2025-07-21 01:28:16.788 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:28:16.793 +08:00 [INF] Hosting environment: Production
2025-07-21 01:28:16.794 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:28:16.794 +08:00 [DBG] Hosting started
2025-07-21 01:28:20.655 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:28:20.807 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:28:21.734 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:28:21.735 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:28:24.212 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:28:24.241 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:28:24.241 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:28:24.454 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:28:24.455 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:28:24.471 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:28:24.472 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:28:24.472 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:28:28.207 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:28:28.208 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:28:28.211 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:28:28.307 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 01:28:28.308 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 100 毫秒
2025-07-21 01:28:30.889 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:28:30.889 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:28:30.890 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:28:30.918 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:28:30.919 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 29 毫秒
2025-07-21 01:28:34.394 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:28:34.395 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:28:34.397 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:28:35.147 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:28:35.695 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:28:35.696 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:28:35.711 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:28:35.712 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:28:35.776 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:28:35.776 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:28:35.780 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:28:35.781 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:28:35.782 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:28:35.873 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:28:35.873 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 91 毫秒
2025-07-21 01:28:35.877 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:28:35.877 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:28:35.877 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:28:35.931 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:28:35.931 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 54 毫秒
2025-07-21 01:28:39.642 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:28:39.643 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:29:03.409 +08:00 [DBG] Hosting starting
2025-07-21 01:29:03.531 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:29:03.543 +08:00 [INF] Hosting environment: Production
2025-07-21 01:29:03.544 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:29:03.545 +08:00 [DBG] Hosting started
2025-07-21 01:29:09.918 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:29:10.082 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:29:11.359 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:29:11.359 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:29:13.163 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:29:13.184 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:29:13.184 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:29:13.376 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:29:13.377 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:29:13.391 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:29:13.391 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:29:13.391 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:29:18.584 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:29:18.586 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:29:18.588 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:29:18.663 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:29:18.664 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 79 毫秒
2025-07-21 01:29:22.784 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:29:22.785 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:29:22.787 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:29:24.328 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:29:24.328 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:29:24.329 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:29:24.354 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:29:24.354 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:29:24.356 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:29:24.357 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:29:24.358 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:29:24.360 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:29:24.360 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:29:24.360 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:29:24.360 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:29:24.369 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:29:24.458 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:29:24.458 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 97 毫秒
2025-07-21 01:29:24.512 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:29:24.522 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:29:24.522 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:29:24.523 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:29:24.523 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 153 毫秒
2025-07-21 01:29:24.525 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:29:24.579 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:29:24.579 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 56 毫秒
2025-07-21 01:29:24.602 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:29:24.602 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:29:24.659 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:29:24.660 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 57 毫秒
2025-07-21 01:29:42.439 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:29:42.440 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:34:33.238 +08:00 [DBG] Hosting starting
2025-07-21 01:34:33.301 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:34:33.305 +08:00 [INF] Hosting environment: Production
2025-07-21 01:34:33.305 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:34:33.306 +08:00 [DBG] Hosting started
2025-07-21 01:34:36.796 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:34:36.798 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:34:40.970 +08:00 [DBG] Hosting starting
2025-07-21 01:34:40.992 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:34:40.994 +08:00 [INF] Hosting environment: Production
2025-07-21 01:34:40.994 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:34:40.994 +08:00 [DBG] Hosting started
2025-07-21 01:34:43.598 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:34:43.687 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:34:45.495 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:34:45.496 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:34:47.030 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:34:47.040 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:34:47.040 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:34:47.138 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:34:47.139 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:34:47.148 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:34:47.148 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:34:47.148 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:34:51.198 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:34:51.200 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:34:51.202 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:34:51.274 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:34:51.275 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 76 毫秒
2025-07-21 01:34:52.871 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:34:52.871 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:34:52.873 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:34:52.892 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:34:52.892 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:34:52.892 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:34:52.905 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:34:52.905 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:34:52.910 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:34:52.910 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:34:52.912 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:34:52.913 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:34:52.913 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:34:53.010 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:34:53.011 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 97 毫秒
2025-07-21 01:34:53.013 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:34:53.013 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:34:53.013 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:34:53.069 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:34:53.069 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 56 毫秒
2025-07-21 01:34:56.649 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:34:56.650 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:42:37.283 +08:00 [DBG] Hosting starting
2025-07-21 01:42:37.382 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:42:37.393 +08:00 [INF] Hosting environment: Production
2025-07-21 01:42:37.395 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:42:37.395 +08:00 [DBG] Hosting started
2025-07-21 01:42:40.654 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:42:40.813 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:42:41.803 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:42:41.804 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:42:44.074 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:42:44.108 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:42:44.108 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:42:44.422 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:42:44.423 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:42:44.440 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:42:44.440 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:42:44.441 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:42:47.282 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:42:47.283 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:42:47.286 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:42:47.368 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:42:47.369 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 87 毫秒
2025-07-21 01:42:50.178 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:42:50.180 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:42:50.183 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:42:51.726 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 01:42:51.726 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 01:42:51.726 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 01:42:51.734 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:42:51.737 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:42:51.737 +08:00 [INF] 返回函數原始碼: 長度=152, 內容預覽=FUNCTION Ch_Date
(
DD in Date
)
RETURN VARCHAR2 AS
2025-07-21 01:42:51.885 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 01:42:51.905 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 01:42:51.905 +08:00 [INF] 返回函數原始碼: 長度=152, 內容預覽=FUNCTION Ch_Date
(
DD in Date
)
RETURN VARCHAR2 AS
2025-07-21 01:42:51.908 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 01:42:51.910 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 01:42:51.910 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:42:51.996 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:42:51.997 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 87 毫秒
2025-07-21 01:42:52.003 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 01:42:52.003 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 01:42:52.003 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:42:52.061 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 01:42:52.063 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 59 毫秒
2025-07-21 01:42:59.915 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:42:59.915 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:42:59.915 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:43:00.751 +08:00 [INF] 正在取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:43:01.309 +08:00 [INF] 正在取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION
2025-07-21 01:43:01.311 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_BASE_GET_AU, TYPE=FUNCTION
2025-07-21 01:43:01.324 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:43:01.325 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:43:01.325 +08:00 [INF] 返回函數原始碼: 長度=1099, 內容預覽=FUNCTION to_base_get_au
  (
  P_TO_NO IN VARCHAR2,
2025-07-21 01:43:01.388 +08:00 [INF] 成功取得物件原始碼: TO_BASE_GET_AU, 類型: FUNCTION, 行數: 27, 內容長度: 1099
2025-07-21 01:43:01.388 +08:00 [INF] 成功取得函數原始碼: TO_BASE_GET_AU
2025-07-21 01:43:01.388 +08:00 [INF] 返回函數原始碼: 長度=1099, 內容預覽=FUNCTION to_base_get_au
  (
  P_TO_NO IN VARCHAR2,
2025-07-21 01:43:01.388 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:43:01.388 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:43:01.388 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:43:01.477 +08:00 [INF] 查詢執行成功，返回 7 筆資料
2025-07-21 01:43:01.477 +08:00 [INF] 查詢執行成功，返回 7 筆資料，耗時 88 毫秒
2025-07-21 01:43:01.478 +08:00 [INF] 正在執行 SQL 語句，長度: 1140
2025-07-21 01:43:01.478 +08:00 [INF] 正在執行查詢，SQL 長度: 1140
2025-07-21 01:43:01.478 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:43:01.532 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:43:01.532 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 54 毫秒
2025-07-21 01:43:09.251 +08:00 [INF] 正在取得函數原始碼: TO_WHATTYPE
2025-07-21 01:43:09.251 +08:00 [INF] 正在取得物件原始碼: TO_WHATTYPE, 類型: FUNCTION
2025-07-21 01:43:09.251 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_WHATTYPE, TYPE=FUNCTION
2025-07-21 01:43:10.219 +08:00 [INF] 正在取得函數原始碼: TO_WHATTYPE
2025-07-21 01:43:10.220 +08:00 [INF] 正在取得物件原始碼: TO_WHATTYPE, 類型: FUNCTION
2025-07-21 01:43:10.220 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_WHATTYPE, TYPE=FUNCTION
2025-07-21 01:43:10.768 +08:00 [INF] 成功取得物件原始碼: TO_WHATTYPE, 類型: FUNCTION, 行數: 28, 內容長度: 462
2025-07-21 01:43:10.769 +08:00 [INF] 成功取得函數原始碼: TO_WHATTYPE
2025-07-21 01:43:10.769 +08:00 [INF] 返回函數原始碼: 長度=462, 內容預覽=FUNCTION TO_WHATTYPE
(
PTONO TO_MAIN.TO_NO%TYPE
)

2025-07-21 01:43:10.813 +08:00 [INF] 成功取得物件原始碼: TO_WHATTYPE, 類型: FUNCTION, 行數: 28, 內容長度: 462
2025-07-21 01:43:10.813 +08:00 [INF] 成功取得函數原始碼: TO_WHATTYPE
2025-07-21 01:43:10.813 +08:00 [INF] 返回函數原始碼: 長度=462, 內容預覽=FUNCTION TO_WHATTYPE
(
PTONO TO_MAIN.TO_NO%TYPE
)

2025-07-21 01:43:10.813 +08:00 [INF] 正在執行 SQL 語句，長度: 724
2025-07-21 01:43:10.813 +08:00 [INF] 正在執行查詢，SQL 長度: 724
2025-07-21 01:43:10.813 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:43:10.910 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:43:10.910 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 97 毫秒
2025-07-21 01:43:10.911 +08:00 [INF] 正在執行 SQL 語句，長度: 1134
2025-07-21 01:43:10.911 +08:00 [INF] 正在執行查詢，SQL 長度: 1134
2025-07-21 01:43:10.911 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:43:11.004 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:43:11.004 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 93 毫秒
2025-07-21 01:43:40.924 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:43:40.924 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:43:40.925 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:43:41.021 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 01:43:41.021 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 96 毫秒
2025-07-21 01:43:43.806 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 01:43:44.276 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 01:43:44.276 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:43:45.460 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 01:43:46.582 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 01:43:46.583 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:43:46.594 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:43:46.594 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:43:46.640 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:43:46.641 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:43:46.642 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:43:46.643 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:43:46.643 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:43:46.730 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:43:46.731 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 87 毫秒
2025-07-21 01:43:46.732 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:43:46.732 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:43:46.732 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:43:46.744 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:43:46.744 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 11 毫秒
2025-07-21 01:43:54.462 +08:00 [INF] 正在取得預存程序原始碼: TOTM_A
2025-07-21 01:43:54.859 +08:00 [INF] 正在取得物件原始碼: TOTM_A, 類型: PROCEDURE
2025-07-21 01:43:54.859 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TOTM_A, TYPE=PROCEDURE
2025-07-21 01:44:02.646 +08:00 [INF] 正在取得預存程序原始碼: TOTM_A
2025-07-21 01:44:05.709 +08:00 [INF] 正在取得物件原始碼: TOTM_A, 類型: PROCEDURE
2025-07-21 01:44:05.710 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TOTM_A, TYPE=PROCEDURE
2025-07-21 01:44:05.719 +08:00 [INF] 成功取得物件原始碼: TOTM_A, 類型: PROCEDURE, 行數: 25, 內容長度: 412
2025-07-21 01:44:05.722 +08:00 [INF] 成功取得預存程序原始碼: TOTM_A
2025-07-21 01:44:08.577 +08:00 [INF] 成功取得物件原始碼: TOTM_A, 類型: PROCEDURE, 行數: 25, 內容長度: 412
2025-07-21 01:44:08.577 +08:00 [INF] 成功取得預存程序原始碼: TOTM_A
2025-07-21 01:44:09.665 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:44:09.665 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:44:09.665 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:44:09.738 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:44:09.738 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 72 毫秒
2025-07-21 01:44:09.738 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:44:09.738 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:44:09.738 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:44:09.794 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 01:44:09.795 +08:00 [INF] 查詢執行成功，返回 5 筆資料，耗時 56 毫秒
2025-07-21 01:44:14.330 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:44:14.332 +08:00 [INF] 所有活動連線已關閉
2025-07-21 01:50:46.664 +08:00 [DBG] Hosting starting
2025-07-21 01:50:46.692 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 01:50:46.696 +08:00 [INF] Hosting environment: Production
2025-07-21 01:50:46.697 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 01:50:46.697 +08:00 [DBG] Hosting started
2025-07-21 01:50:49.401 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:50:49.524 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:50:50.786 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 01:50:50.786 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 01:50:52.551 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 01:50:52.578 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 01:50:52.578 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 01:50:52.779 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 01:50:52.779 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 01:50:52.794 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 01:50:52.794 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 01:50:52.794 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 01:50:55.705 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:50:55.706 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 01:50:55.708 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:50:55.791 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 01:50:55.793 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 87 毫秒
2025-07-21 01:50:58.452 +08:00 [INF] 正在取得函數原始碼: MT_CASEADVHIS
2025-07-21 01:50:58.454 +08:00 [INF] 正在取得物件原始碼: MT_CASEADVHIS, 類型: FUNCTION
2025-07-21 01:50:58.457 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=MT_CASEADVHIS, TYPE=FUNCTION
2025-07-21 01:50:59.933 +08:00 [INF] 正在取得函數原始碼: MT_CASEADVHIS
2025-07-21 01:50:59.933 +08:00 [INF] 正在取得物件原始碼: MT_CASEADVHIS, 類型: FUNCTION
2025-07-21 01:50:59.933 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=MT_CASEADVHIS, TYPE=FUNCTION
2025-07-21 01:50:59.946 +08:00 [INF] 成功取得物件原始碼: MT_CASEADVHIS, 類型: FUNCTION, 行數: 24, 內容長度: 200
2025-07-21 01:50:59.947 +08:00 [INF] 成功取得函數原始碼: MT_CASEADVHIS
2025-07-21 01:50:59.947 +08:00 [INF] 返回函數原始碼: 長度=200, 內容預覽=FUNCTION      MT_CASEADVHIS
(
CNO MT_CASEM.PNO%TYP
2025-07-21 01:51:00.579 +08:00 [INF] 正在執行 SQL 語句，長度: 726
2025-07-21 01:51:00.580 +08:00 [INF] 正在執行查詢，SQL 長度: 726
2025-07-21 01:51:00.580 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:51:00.597 +08:00 [INF] 成功取得物件原始碼: MT_CASEADVHIS, 類型: FUNCTION, 行數: 24, 內容長度: 200
2025-07-21 01:51:00.598 +08:00 [INF] 成功取得函數原始碼: MT_CASEADVHIS
2025-07-21 01:51:00.598 +08:00 [INF] 返回函數原始碼: 長度=200, 內容預覽=FUNCTION      MT_CASEADVHIS
(
CNO MT_CASEM.PNO%TYP
2025-07-21 01:51:01.201 +08:00 [INF] 正在執行 SQL 語句，長度: 726
2025-07-21 01:51:01.201 +08:00 [INF] 正在執行查詢，SQL 長度: 726
2025-07-21 01:51:01.202 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:51:01.298 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:01.299 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 718 毫秒
2025-07-21 01:51:01.301 +08:00 [INF] 正在執行 SQL 語句，長度: 1138
2025-07-21 01:51:01.364 +08:00 [INF] 正在執行查詢，SQL 長度: 1138
2025-07-21 01:51:01.364 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:51:01.366 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:01.366 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 164 毫秒
2025-07-21 01:51:01.367 +08:00 [INF] 正在執行 SQL 語句，長度: 1138
2025-07-21 01:51:01.418 +08:00 [INF] 正在執行查詢，SQL 長度: 1138
2025-07-21 01:51:01.424 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:51:01.425 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:01.426 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 61 毫秒
2025-07-21 01:51:01.435 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:01.435 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 11 毫秒
2025-07-21 01:51:09.846 +08:00 [INF] 正在取得函數原始碼: GET_SUSPEND
2025-07-21 01:51:09.846 +08:00 [INF] 正在取得物件原始碼: GET_SUSPEND, 類型: FUNCTION
2025-07-21 01:51:09.846 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_SUSPEND, TYPE=FUNCTION
2025-07-21 01:51:11.217 +08:00 [INF] 正在取得函數原始碼: GET_SUSPEND
2025-07-21 01:51:11.217 +08:00 [INF] 正在取得物件原始碼: GET_SUSPEND, 類型: FUNCTION
2025-07-21 01:51:11.217 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_SUSPEND, TYPE=FUNCTION
2025-07-21 01:51:11.221 +08:00 [INF] 成功取得物件原始碼: GET_SUSPEND, 類型: FUNCTION, 行數: 9, 內容長度: 205
2025-07-21 01:51:11.222 +08:00 [INF] 成功取得函數原始碼: GET_SUSPEND
2025-07-21 01:51:11.222 +08:00 [INF] 返回函數原始碼: 長度=205, 內容預覽=FUNCTION get_suspend
( SUSPEND_ in TO_MAIN.SUSPEND
2025-07-21 01:51:11.814 +08:00 [INF] 正在執行 SQL 語句，長度: 724
2025-07-21 01:51:11.814 +08:00 [INF] 正在執行查詢，SQL 長度: 724
2025-07-21 01:51:11.814 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:51:11.837 +08:00 [INF] 成功取得物件原始碼: GET_SUSPEND, 類型: FUNCTION, 行數: 9, 內容長度: 205
2025-07-21 01:51:11.837 +08:00 [INF] 成功取得函數原始碼: GET_SUSPEND
2025-07-21 01:51:11.837 +08:00 [INF] 返回函數原始碼: 長度=205, 內容預覽=FUNCTION get_suspend
( SUSPEND_ in TO_MAIN.SUSPEND
2025-07-21 01:51:12.373 +08:00 [INF] 正在執行 SQL 語句，長度: 724
2025-07-21 01:51:12.374 +08:00 [INF] 正在執行查詢，SQL 長度: 724
2025-07-21 01:51:12.374 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:51:12.384 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:51:12.384 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 569 毫秒
2025-07-21 01:51:12.387 +08:00 [INF] 正在執行 SQL 語句，長度: 1134
2025-07-21 01:51:12.448 +08:00 [INF] 正在執行查詢，SQL 長度: 1134
2025-07-21 01:51:12.448 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:51:12.449 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 01:51:12.449 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 75 毫秒
2025-07-21 01:51:12.450 +08:00 [INF] 正在執行 SQL 語句，長度: 1134
2025-07-21 01:51:12.505 +08:00 [INF] 正在執行查詢，SQL 長度: 1134
2025-07-21 01:51:12.505 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:51:12.506 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:51:12.506 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 57 毫秒
2025-07-21 01:51:12.558 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 01:51:12.560 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 54 毫秒
2025-07-21 01:51:19.782 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:51:19.782 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 01:51:19.783 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 01:51:19.798 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 01:51:19.799 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 16 毫秒
2025-07-21 01:51:22.151 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 01:51:22.604 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 01:51:22.604 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:51:35.190 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 01:51:35.190 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 01:51:35.190 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 01:51:35.194 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:51:35.195 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:51:35.940 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:51:35.940 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:51:35.940 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:51:35.961 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 01:51:35.962 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 01:51:36.472 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:36.473 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 532 毫秒
2025-07-21 01:51:36.476 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:51:36.477 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:51:36.477 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:51:36.479 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 01:51:36.488 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 01:51:36.488 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 01:51:36.489 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:36.489 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 12 毫秒
2025-07-21 01:51:36.553 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:36.553 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 65 毫秒
2025-07-21 01:51:36.554 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 01:51:36.554 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 01:51:36.554 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 01:51:36.562 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 01:51:36.562 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 8 毫秒
2025-07-21 01:51:41.509 +08:00 [DBG] 視窗設定已儲存
2025-07-21 01:51:41.513 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:00:32.401 +08:00 [DBG] Hosting starting
2025-07-21 02:00:32.440 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:00:32.443 +08:00 [INF] Hosting environment: Production
2025-07-21 02:00:32.444 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:00:32.444 +08:00 [DBG] Hosting started
2025-07-21 02:00:49.149 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:00:49.289 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:00:51.109 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:00:51.110 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:00:53.018 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:00:53.042 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:00:53.042 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:00:53.241 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:00:53.242 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:00:53.254 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:00:53.254 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:00:53.255 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:00:56.621 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:00:56.622 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:00:56.625 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:00:56.723 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 02:00:56.725 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 103 毫秒
2025-07-21 02:01:02.452 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 02:01:02.454 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 02:01:02.456 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 02:01:05.115 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 02:01:05.116 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 02:01:05.116 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 02:01:05.125 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 02:01:05.126 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 02:01:07.340 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 02:01:07.344 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 02:01:07.344 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:01:07.386 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 02:01:07.387 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 02:01:07.906 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 02:01:07.906 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 02:01:07.906 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:01:07.907 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:01:07.907 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 563 毫秒
2025-07-21 02:01:07.911 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:01:07.912 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:01:07.912 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:01:07.974 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:01:07.974 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 67 毫秒
2025-07-21 02:01:07.974 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:01:07.988 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:01:07.988 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:01:07.990 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:01:07.990 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 77 毫秒
2025-07-21 02:01:08.002 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:01:08.003 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 14 毫秒
2025-07-21 02:01:13.073 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:01:13.073 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:01:13.074 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:01:13.086 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 02:01:13.086 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 13 毫秒
2025-07-21 02:01:15.085 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 02:01:15.085 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 02:01:15.085 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 02:01:15.266 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 02:01:15.266 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 02:01:15.266 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 02:01:15.270 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 02:01:15.271 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 02:01:15.271 +08:00 [INF] 返回函數原始碼: 長度=170, 內容預覽=FUNCTION GET_MEMO
( 
param1 IN VARCHAR2
)
RETURN  
2025-07-21 02:01:16.188 +08:00 [INF] 正在執行 SQL 語句，長度: 721
2025-07-21 02:01:16.188 +08:00 [INF] 正在執行查詢，SQL 長度: 721
2025-07-21 02:01:16.188 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:01:16.200 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 02:01:16.201 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 02:01:16.201 +08:00 [INF] 返回函數原始碼: 長度=170, 內容預覽=FUNCTION GET_MEMO
( 
param1 IN VARCHAR2
)
RETURN  
2025-07-21 02:01:16.694 +08:00 [INF] 正在執行 SQL 語句，長度: 721
2025-07-21 02:01:16.696 +08:00 [INF] 正在執行查詢，SQL 長度: 721
2025-07-21 02:01:16.696 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:01:16.698 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 02:01:16.698 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 509 毫秒
2025-07-21 02:01:16.700 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:01:16.709 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:01:16.709 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:01:16.774 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 02:01:16.774 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 78 毫秒
2025-07-21 02:01:16.775 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:01:16.829 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:01:16.830 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:01:16.831 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:01:16.832 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 122 毫秒
2025-07-21 02:01:16.885 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:01:16.885 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 55 毫秒
2025-07-21 02:01:21.615 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 02:01:21.615 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 02:01:21.616 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:01:21.684 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 02:01:21.685 +08:00 [INF] 成功取得 5 個 "Package" 物件，耗時 69 毫秒
2025-07-21 02:01:23.584 +08:00 [INF] 開始載入套件定義: TO_EIS
2025-07-21 02:01:23.585 +08:00 [INF] 正在取得套件定義: TO_EIS
2025-07-21 02:01:24.304 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE
2025-07-21 02:01:24.305 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE
2025-07-21 02:01:24.323 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE, 行數: 72, 內容長度: 1688
2025-07-21 02:01:24.323 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE BODY
2025-07-21 02:01:24.323 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE BODY
2025-07-21 02:01:24.393 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE BODY, 行數: 313, 內容長度: 10149
2025-07-21 02:01:24.928 +08:00 [INF] 成功取得套件定義: TO_EIS
2025-07-21 02:01:24.928 +08:00 [INF] 從服務取得套件定義: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 02:01:24.928 +08:00 [INF] PackageDefinition 屬性已設置: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 02:01:24.929 +08:00 [INF] 開始設置原始碼內容，_isInitializing = true
2025-07-21 02:01:24.929 +08:00 [INF] 準備設置原始碼內容 - 規格長度: 1688, 主體長度: 10149
2025-07-21 02:01:24.932 +08:00 [INF] 規格原始碼已設置，實際長度: 1688
2025-07-21 02:01:24.935 +08:00 [INF] 主體原始碼已設置，實際長度: 10149
2025-07-21 02:01:24.935 +08:00 [INF] 原始碼內容設置完成，_isInitializing = false
2025-07-21 02:01:24.937 +08:00 [INF] 正在執行 SQL 語句，長度: 1120
2025-07-21 02:01:24.937 +08:00 [INF] 正在執行查詢，SQL 長度: 1120
2025-07-21 02:01:24.937 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:01:25.061 +08:00 [INF] 查詢執行成功，返回 9 筆資料
2025-07-21 02:01:25.061 +08:00 [INF] 查詢執行成功，返回 9 筆資料，耗時 124 毫秒
2025-07-21 02:02:10.840 +08:00 [INF] 開始載入套件定義: TO_PACK1
2025-07-21 02:02:10.840 +08:00 [INF] 正在取得套件定義: TO_PACK1
2025-07-21 02:02:10.841 +08:00 [INF] 正在取得物件原始碼: TO_PACK1, 類型: PACKAGE
2025-07-21 02:02:10.841 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_PACK1, TYPE=PACKAGE
2025-07-21 02:02:10.860 +08:00 [INF] 成功取得物件原始碼: TO_PACK1, 類型: PACKAGE, 行數: 7, 內容長度: 129
2025-07-21 02:02:10.860 +08:00 [INF] 正在取得物件原始碼: TO_PACK1, 類型: PACKAGE BODY
2025-07-21 02:02:10.860 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_PACK1, TYPE=PACKAGE BODY
2025-07-21 02:02:10.865 +08:00 [INF] 成功取得物件原始碼: TO_PACK1, 類型: PACKAGE BODY, 行數: 0, 內容長度: 0
2025-07-21 02:02:10.870 +08:00 [INF] 成功取得套件定義: TO_PACK1
2025-07-21 02:02:11.592 +08:00 [INF] 從服務取得套件定義: TO_PACK1, 規格長度: 129, 主體長度: 0
2025-07-21 02:02:11.592 +08:00 [INF] PackageDefinition 屬性已設置: TO_PACK1, 規格長度: 129, 主體長度: 0
2025-07-21 02:02:11.592 +08:00 [INF] 開始設置原始碼內容，_isInitializing = true
2025-07-21 02:02:11.592 +08:00 [INF] 準備設置原始碼內容 - 規格長度: 129, 主體長度: 0
2025-07-21 02:02:11.594 +08:00 [INF] 規格原始碼已設置，實際長度: 129
2025-07-21 02:02:11.594 +08:00 [INF] 主體原始碼已設置，實際長度: 0
2025-07-21 02:02:11.594 +08:00 [INF] 原始碼內容設置完成，_isInitializing = false
2025-07-21 02:02:11.594 +08:00 [INF] 正在執行 SQL 語句，長度: 1124
2025-07-21 02:02:11.594 +08:00 [INF] 正在執行查詢，SQL 長度: 1124
2025-07-21 02:02:11.594 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:02:11.644 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:02:11.644 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 49 毫秒
2025-07-21 02:02:39.032 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:02:39.032 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:02:39.032 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:02:39.048 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 02:02:39.049 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 16 毫秒
2025-07-21 02:02:39.052 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:02:39.052 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:02:39.052 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:02:39.062 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 02:02:39.063 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 10 毫秒
2025-07-21 02:02:39.064 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 02:02:39.064 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 02:02:39.064 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:02:39.069 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 02:02:39.069 +08:00 [INF] 成功取得 4 個 "Package" 物件，耗時 5 毫秒
2025-07-21 02:02:42.490 +08:00 [INF] 開始載入套件定義: PKTEST
2025-07-21 02:02:42.490 +08:00 [INF] 正在取得套件定義: PKTEST
2025-07-21 02:02:42.490 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE
2025-07-21 02:02:42.490 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE
2025-07-21 02:02:42.496 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE, 行數: 5, 內容長度: 120
2025-07-21 02:02:42.497 +08:00 [INF] 正在取得物件原始碼: PKTEST, 類型: PACKAGE BODY
2025-07-21 02:02:42.497 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PKTEST, TYPE=PACKAGE BODY
2025-07-21 02:02:42.505 +08:00 [INF] 成功取得物件原始碼: PKTEST, 類型: PACKAGE BODY, 行數: 7, 內容長度: 163
2025-07-21 02:02:42.511 +08:00 [INF] 成功取得套件定義: PKTEST
2025-07-21 02:02:43.231 +08:00 [INF] 從服務取得套件定義: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 02:02:43.231 +08:00 [INF] PackageDefinition 屬性已設置: PKTEST, 規格長度: 120, 主體長度: 163
2025-07-21 02:02:43.231 +08:00 [INF] 開始設置原始碼內容，_isInitializing = true
2025-07-21 02:02:43.231 +08:00 [INF] 準備設置原始碼內容 - 規格長度: 120, 主體長度: 163
2025-07-21 02:02:43.235 +08:00 [INF] 規格原始碼已設置，實際長度: 120
2025-07-21 02:02:43.239 +08:00 [INF] 主體原始碼已設置，實際長度: 163
2025-07-21 02:02:43.239 +08:00 [INF] 原始碼內容設置完成，_isInitializing = false
2025-07-21 02:02:43.239 +08:00 [INF] 正在執行 SQL 語句，長度: 1120
2025-07-21 02:02:43.239 +08:00 [INF] 正在執行查詢，SQL 長度: 1120
2025-07-21 02:02:43.239 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:02:43.253 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:02:43.253 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 14 毫秒
2025-07-21 02:02:50.215 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:02:50.216 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:07:28.444 +08:00 [DBG] Hosting starting
2025-07-21 02:07:28.534 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:07:28.546 +08:00 [INF] Hosting environment: Production
2025-07-21 02:07:28.547 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:07:28.547 +08:00 [DBG] Hosting started
2025-07-21 02:07:32.547 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:07:32.694 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:07:33.627 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:07:33.628 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:07:35.699 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:07:35.720 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:07:35.720 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:07:35.921 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:07:35.922 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:07:35.936 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:07:35.936 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:07:35.937 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:07:38.718 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:07:38.719 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:07:38.721 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:07:38.819 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 02:07:38.821 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 102 毫秒
2025-07-21 02:07:39.945 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 02:07:39.946 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 02:07:39.947 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 02:07:40.195 +08:00 [INF] 正在取得預存程序原始碼: TESTXX
2025-07-21 02:07:40.196 +08:00 [INF] 正在取得物件原始碼: TESTXX, 類型: PROCEDURE
2025-07-21 02:07:40.196 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TESTXX, TYPE=PROCEDURE
2025-07-21 02:07:40.197 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 02:07:40.197 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 02:07:41.749 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 02:07:41.750 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 02:07:41.750 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:07:41.771 +08:00 [INF] 成功取得物件原始碼: TESTXX, 類型: PROCEDURE, 行數: 8, 內容長度: 88
2025-07-21 02:07:41.771 +08:00 [INF] 成功取得預存程序原始碼: TESTXX
2025-07-21 02:07:42.383 +08:00 [INF] 正在執行 SQL 語句，長度: 727
2025-07-21 02:07:42.383 +08:00 [INF] 正在執行查詢，SQL 長度: 727
2025-07-21 02:07:42.383 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:07:42.385 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:07:42.385 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 635 毫秒
2025-07-21 02:07:42.390 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:07:42.391 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:07:42.452 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:07:42.454 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:07:42.454 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 70 毫秒
2025-07-21 02:07:42.455 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:07:42.470 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:07:42.470 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:07:42.473 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:07:42.474 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 21 毫秒
2025-07-21 02:07:42.482 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:07:42.482 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 11 毫秒
2025-07-21 02:07:48.761 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:07:48.761 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:07:48.762 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:07:48.773 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 02:07:48.773 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 11 毫秒
2025-07-21 02:07:50.110 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 02:07:50.110 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 02:07:50.110 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 02:07:50.316 +08:00 [INF] 正在取得函數原始碼: GET_MEMO
2025-07-21 02:07:50.316 +08:00 [INF] 正在取得物件原始碼: GET_MEMO, 類型: FUNCTION
2025-07-21 02:07:50.316 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=GET_MEMO, TYPE=FUNCTION
2025-07-21 02:07:50.322 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 02:07:50.323 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 02:07:50.323 +08:00 [INF] 返回函數原始碼: 長度=170, 內容預覽=FUNCTION GET_MEMO
( 
param1 IN VARCHAR2
)
RETURN  
2025-07-21 02:07:51.567 +08:00 [INF] 正在執行 SQL 語句，長度: 721
2025-07-21 02:07:51.567 +08:00 [INF] 正在執行查詢，SQL 長度: 721
2025-07-21 02:07:51.568 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:07:51.584 +08:00 [INF] 成功取得物件原始碼: GET_MEMO, 類型: FUNCTION, 行數: 13, 內容長度: 170
2025-07-21 02:07:51.584 +08:00 [INF] 成功取得函數原始碼: GET_MEMO
2025-07-21 02:07:51.584 +08:00 [INF] 返回函數原始碼: 長度=170, 內容預覽=FUNCTION GET_MEMO
( 
param1 IN VARCHAR2
)
RETURN  
2025-07-21 02:07:52.193 +08:00 [INF] 正在執行 SQL 語句，長度: 721
2025-07-21 02:07:52.194 +08:00 [INF] 正在執行查詢，SQL 長度: 721
2025-07-21 02:07:52.194 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:07:52.197 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 02:07:52.198 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 630 毫秒
2025-07-21 02:07:52.201 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:07:52.201 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:07:52.201 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:07:52.297 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 02:07:52.298 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 104 毫秒
2025-07-21 02:07:52.299 +08:00 [INF] 正在執行 SQL 語句，長度: 1128
2025-07-21 02:07:52.345 +08:00 [INF] 正在執行查詢，SQL 長度: 1128
2025-07-21 02:07:52.345 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:07:52.347 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:07:52.347 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 146 毫秒
2025-07-21 02:07:52.404 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:07:52.405 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 59 毫秒
2025-07-21 02:07:57.690 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 02:07:57.690 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 02:07:57.691 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:07:57.701 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 02:07:57.702 +08:00 [INF] 成功取得 4 個 "Package" 物件，耗時 11 毫秒
2025-07-21 02:07:59.079 +08:00 [INF] 開始載入套件定義: TO_EIS
2025-07-21 02:07:59.080 +08:00 [INF] 正在取得套件定義: TO_EIS
2025-07-21 02:07:59.080 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE
2025-07-21 02:07:59.080 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE
2025-07-21 02:07:59.116 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE, 行數: 72, 內容長度: 1688
2025-07-21 02:07:59.117 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE BODY
2025-07-21 02:07:59.117 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE BODY
2025-07-21 02:07:59.186 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE BODY, 行數: 313, 內容長度: 10149
2025-07-21 02:07:59.194 +08:00 [INF] 成功取得套件定義: TO_EIS
2025-07-21 02:08:00.416 +08:00 [INF] 從服務取得套件定義: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 02:08:00.418 +08:00 [INF] PackageDefinition 屬性已設置: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 02:08:00.418 +08:00 [INF] 開始設置原始碼內容，_isInitializing = true
2025-07-21 02:08:00.418 +08:00 [INF] 準備設置原始碼內容 - 規格長度: 1688, 主體長度: 10149
2025-07-21 02:08:00.425 +08:00 [INF] 規格原始碼已設置，實際長度: 1688
2025-07-21 02:08:00.430 +08:00 [INF] 主體原始碼已設置，實際長度: 10149
2025-07-21 02:08:00.430 +08:00 [INF] 原始碼內容設置完成，_isInitializing = false
2025-07-21 02:08:00.433 +08:00 [INF] 正在執行 SQL 語句，長度: 1120
2025-07-21 02:08:00.433 +08:00 [INF] 正在執行查詢，SQL 長度: 1120
2025-07-21 02:08:00.433 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:08:00.550 +08:00 [INF] 查詢執行成功，返回 9 筆資料
2025-07-21 02:08:00.550 +08:00 [INF] 查詢執行成功，返回 9 筆資料，耗時 116 毫秒
2025-07-21 02:08:14.001 +08:00 [INF] 正在取得預存程序原始碼: TO_GET_QUERY_CODE
2025-07-21 02:08:14.001 +08:00 [INF] 正在取得物件原始碼: TO_GET_QUERY_CODE, 類型: PROCEDURE
2025-07-21 02:08:14.001 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_GET_QUERY_CODE, TYPE=PROCEDURE
2025-07-21 02:08:14.122 +08:00 [INF] 正在取得預存程序原始碼: TO_GET_QUERY_CODE
2025-07-21 02:08:14.122 +08:00 [INF] 正在取得物件原始碼: TO_GET_QUERY_CODE, 類型: PROCEDURE
2025-07-21 02:08:14.122 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_GET_QUERY_CODE, TYPE=PROCEDURE
2025-07-21 02:08:14.127 +08:00 [INF] 成功取得物件原始碼: TO_GET_QUERY_CODE, 類型: PROCEDURE, 行數: 28, 內容長度: 468
2025-07-21 02:08:14.127 +08:00 [INF] 成功取得預存程序原始碼: TO_GET_QUERY_CODE
2025-07-21 02:08:15.688 +08:00 [INF] 正在執行 SQL 語句，長度: 738
2025-07-21 02:08:15.688 +08:00 [INF] 正在執行查詢，SQL 長度: 738
2025-07-21 02:08:15.689 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:08:15.722 +08:00 [INF] 成功取得物件原始碼: TO_GET_QUERY_CODE, 類型: PROCEDURE, 行數: 28, 內容長度: 468
2025-07-21 02:08:15.723 +08:00 [INF] 成功取得預存程序原始碼: TO_GET_QUERY_CODE
2025-07-21 02:08:16.523 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:08:16.526 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 837 毫秒
2025-07-21 02:08:16.531 +08:00 [INF] 正在執行 SQL 語句，長度: 1150
2025-07-21 02:08:16.531 +08:00 [INF] 正在執行查詢，SQL 長度: 1150
2025-07-21 02:08:16.531 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:08:16.531 +08:00 [INF] 正在執行 SQL 語句，長度: 738
2025-07-21 02:08:16.648 +08:00 [INF] 正在執行查詢，SQL 長度: 738
2025-07-21 02:08:16.648 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:08:16.650 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:08:16.650 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 118 毫秒
2025-07-21 02:08:16.722 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:08:16.722 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 74 毫秒
2025-07-21 02:08:16.722 +08:00 [INF] 正在執行 SQL 語句，長度: 1150
2025-07-21 02:08:16.722 +08:00 [INF] 正在執行查詢，SQL 長度: 1150
2025-07-21 02:08:16.722 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:08:16.772 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:08:16.772 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 49 毫秒
2025-07-21 02:08:39.747 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:08:39.750 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:08:56.264 +08:00 [DBG] Hosting starting
2025-07-21 02:08:56.302 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:08:56.305 +08:00 [INF] Hosting environment: Production
2025-07-21 02:08:56.305 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:08:56.305 +08:00 [DBG] Hosting started
2025-07-21 02:09:07.283 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:09:07.478 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:09:08.391 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:09:08.392 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:09:10.062 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:09:10.088 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:09:10.088 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:09:10.263 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:09:10.263 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:09:10.274 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:09:10.274 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:09:10.274 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:09:13.181 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:09:13.182 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:09:13.184 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 02:09:13.386 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 02:09:13.387 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 206 毫秒
2025-07-21 02:09:15.612 +08:00 [INF] 正在取得檢視表定義: TO_EXAM_UCODE
2025-07-21 02:09:16.233 +08:00 [INF] 成功取得檢視表定義: TO_EXAM_UCODE
2025-07-21 02:09:16.235 +08:00 [INF] 正在執行 SQL 語句，長度: 755
2025-07-21 02:09:16.236 +08:00 [INF] 正在執行查詢，SQL 長度: 755
2025-07-21 02:09:16.237 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:09:16.510 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_EXAM_UCODE'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:09:16.612 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:09:16.725 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:09:16.729 +08:00 [INF] 正在執行 SQL 語句，長度: 1122
2025-07-21 02:09:16.730 +08:00 [INF] 正在執行查詢，SQL 長度: 1122
2025-07-21 02:09:16.730 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:09:16.835 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:09:16.835 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 105 毫秒
2025-07-21 02:09:42.962 +08:00 [INF] 正在取得檢視表定義: TO_INDEX1
2025-07-21 02:09:42.973 +08:00 [INF] 成功取得檢視表定義: TO_INDEX1
2025-07-21 02:09:46.252 +08:00 [INF] 正在執行 SQL 語句，長度: 751
2025-07-21 02:09:46.253 +08:00 [INF] 正在執行查詢，SQL 長度: 751
2025-07-21 02:09:46.253 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:09:46.373 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_INDEX1'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:09:46.419 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:09:46.481 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:09:46.482 +08:00 [INF] 正在執行 SQL 語句，長度: 1114
2025-07-21 02:09:46.482 +08:00 [INF] 正在執行查詢，SQL 長度: 1114
2025-07-21 02:09:46.482 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:09:46.492 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:09:46.492 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 9 毫秒
2025-07-21 02:10:01.780 +08:00 [INF] 正在取得檢視表定義: MEMBER_VIEW
2025-07-21 02:11:07.646 +08:00 [INF] 成功取得檢視表定義: MEMBER_VIEW
2025-07-21 02:11:08.267 +08:00 [INF] 正在執行 SQL 語句，長度: 753
2025-07-21 02:11:08.267 +08:00 [INF] 正在執行查詢，SQL 長度: 753
2025-07-21 02:11:08.267 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:11:08.432 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'MEMBER_VIEW'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0()
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
2025-07-21 02:11:08.490 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0()
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql)
2025-07-21 02:11:08.572 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0()
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql)
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql)
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken)
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken)
2025-07-21 02:11:08.574 +08:00 [INF] 正在執行 SQL 語句，長度: 1118
2025-07-21 02:11:08.574 +08:00 [INF] 正在執行查詢，SQL 長度: 1118
2025-07-21 02:11:08.574 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:11:08.649 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:11:08.649 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 75 毫秒
2025-07-21 02:11:14.467 +08:00 [INF] 正在取得檢視表定義: MEMBER_VIEW
2025-07-21 02:11:59.063 +08:00 [INF] 成功取得檢視表定義: MEMBER_VIEW
2025-07-21 02:12:08.307 +08:00 [INF] 正在執行 SQL 語句，長度: 753
2025-07-21 02:12:08.308 +08:00 [INF] 正在執行查詢，SQL 長度: 753
2025-07-21 02:12:08.308 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:12:08.466 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'MEMBER_VIEW'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0()
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
2025-07-21 02:12:08.526 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0()
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql)
2025-07-21 02:12:08.615 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0()
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql)
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql)
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql)
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken)
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken)
2025-07-21 02:12:08.617 +08:00 [INF] 正在執行 SQL 語句，長度: 1118
2025-07-21 02:12:08.617 +08:00 [INF] 正在執行查詢，SQL 長度: 1118
2025-07-21 02:12:08.617 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:12:08.627 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:12:08.627 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 9 毫秒
2025-07-21 02:12:47.629 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:12:47.631 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:15:20.541 +08:00 [DBG] Hosting starting
2025-07-21 02:15:20.595 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:15:20.601 +08:00 [INF] Hosting environment: Production
2025-07-21 02:15:20.602 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:15:20.602 +08:00 [DBG] Hosting started
2025-07-21 02:15:25.509 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:15:25.659 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:15:30.723 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:15:30.723 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:15:32.538 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:15:32.565 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:15:32.565 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:15:32.760 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:15:32.761 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:15:32.775 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:15:32.778 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:15:32.779 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:15:36.968 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:15:36.969 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:15:36.971 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 02:15:37.049 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 02:15:37.050 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 81 毫秒
2025-07-21 02:15:38.685 +08:00 [INF] 正在取得檢視表定義: MEMBER_VIEW
2025-07-21 02:15:51.617 +08:00 [INF] 成功取得檢視表定義: MEMBER_VIEW
2025-07-21 02:15:52.520 +08:00 [INF] 正在執行 SQL 語句，長度: 753
2025-07-21 02:15:52.523 +08:00 [INF] 正在執行查詢，SQL 長度: 753
2025-07-21 02:15:52.523 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:15:52.708 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'MEMBER_VIEW'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:15:52.787 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:15:52.865 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:15:52.872 +08:00 [INF] 正在執行 SQL 語句，長度: 1118
2025-07-21 02:15:52.872 +08:00 [INF] 正在執行查詢，SQL 長度: 1118
2025-07-21 02:15:52.872 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:15:52.882 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:15:52.882 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 10 毫秒
2025-07-21 02:15:54.579 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:15:54.581 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:18:10.128 +08:00 [DBG] Hosting starting
2025-07-21 02:18:10.210 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:18:10.217 +08:00 [INF] Hosting environment: Production
2025-07-21 02:18:10.218 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:18:10.218 +08:00 [DBG] Hosting started
2025-07-21 02:18:13.225 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:18:13.357 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:18:14.873 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:18:14.875 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:18:16.813 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:18:16.847 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:18:16.847 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:18:17.086 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:18:17.086 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:18:17.112 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:18:17.113 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:18:17.113 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:19:18.758 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:19:18.760 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:19:18.762 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 02:19:18.862 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 02:19:18.863 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 104 毫秒
2025-07-21 02:19:20.070 +08:00 [INF] 正在取得檢視表定義: MEMBER_VIEW
2025-07-21 02:19:53.102 +08:00 [INF] 成功取得檢視表定義: MEMBER_VIEW
2025-07-21 02:20:36.843 +08:00 [INF] 正在執行 SQL 語句，長度: 753
2025-07-21 02:20:36.847 +08:00 [INF] 正在執行查詢，SQL 長度: 753
2025-07-21 02:20:36.848 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:20:37.086 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'MEMBER_VIEW'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:20:37.171 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:20:37.240 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:20:37.246 +08:00 [INF] 正在執行 SQL 語句，長度: 1118
2025-07-21 02:20:37.247 +08:00 [INF] 正在執行查詢，SQL 長度: 1118
2025-07-21 02:20:37.247 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:20:37.262 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:20:37.263 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 16 毫秒
2025-07-21 02:24:21.443 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:24:21.444 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:25:27.941 +08:00 [DBG] Hosting starting
2025-07-21 02:25:28.065 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:25:28.074 +08:00 [INF] Hosting environment: Production
2025-07-21 02:25:28.075 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:25:28.076 +08:00 [DBG] Hosting started
2025-07-21 02:25:36.907 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:25:37.070 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:25:38.126 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:25:38.126 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:25:40.192 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:25:40.232 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:25:40.232 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:25:40.454 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:25:40.454 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:25:40.469 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:25:40.470 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:25:40.470 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:25:43.237 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:25:43.239 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:25:43.242 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 02:25:43.454 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 02:25:43.456 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 219 毫秒
2025-07-21 02:25:44.695 +08:00 [INF] 正在取得檢視表定義: MEMBER_VIEW
2025-07-21 02:25:53.725 +08:00 [INF] 成功取得檢視表定義: MEMBER_VIEW
2025-07-21 02:25:56.696 +08:00 [INF] 正在執行 SQL 語句，長度: 753
2025-07-21 02:25:56.698 +08:00 [INF] 正在執行查詢，SQL 長度: 753
2025-07-21 02:25:56.698 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:25:56.864 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'MEMBER_VIEW'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:25:56.945 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:25:57.021 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:25:57.024 +08:00 [INF] 正在執行 SQL 語句，長度: 1118
2025-07-21 02:25:57.024 +08:00 [INF] 正在執行查詢，SQL 長度: 1118
2025-07-21 02:25:57.024 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:25:57.034 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:25:57.034 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 9 毫秒
2025-07-21 02:26:09.264 +08:00 [INF] 正在取得檢視表定義: TO_BASE_DEAL_WARN_V1
2025-07-21 02:26:17.242 +08:00 [INF] 成功取得檢視表定義: TO_BASE_DEAL_WARN_V1
2025-07-21 02:26:19.552 +08:00 [INF] 正在執行 SQL 語句，長度: 762
2025-07-21 02:26:19.553 +08:00 [INF] 正在執行查詢，SQL 長度: 762
2025-07-21 02:26:19.553 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:26:19.691 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_BASE_DEAL_WARN_V1'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:26:19.741 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:26:19.813 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:26:19.815 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 02:26:19.815 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 02:26:19.815 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:26:19.948 +08:00 [INF] 查詢執行成功，返回 6 筆資料
2025-07-21 02:26:19.948 +08:00 [INF] 查詢執行成功，返回 6 筆資料，耗時 133 毫秒
2025-07-21 02:26:23.547 +08:00 [INF] 正在取得檢視表定義: TO_EXAM_DETAIL_V1
2025-07-21 02:26:23.864 +08:00 [INF] 成功取得檢視表定義: TO_EXAM_DETAIL_V1
2025-07-21 02:26:28.160 +08:00 [INF] 正在執行 SQL 語句，長度: 759
2025-07-21 02:26:28.160 +08:00 [INF] 正在執行查詢，SQL 長度: 759
2025-07-21 02:26:28.160 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:26:28.309 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_EXAM_DETAIL_V1'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:26:28.355 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:26:28.464 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:26:28.467 +08:00 [INF] 正在執行 SQL 語句，長度: 1130
2025-07-21 02:26:28.467 +08:00 [INF] 正在執行查詢，SQL 長度: 1130
2025-07-21 02:26:28.467 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:26:28.570 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 02:26:28.572 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 105 毫秒
2025-07-21 02:26:46.390 +08:00 [INF] 正在取得檢視表定義: TO_EXAM_UCODE
2025-07-21 02:26:46.712 +08:00 [INF] 成功取得檢視表定義: TO_EXAM_UCODE
2025-07-21 02:26:46.712 +08:00 [INF] 正在執行 SQL 語句，長度: 755
2025-07-21 02:26:46.712 +08:00 [INF] 正在執行查詢，SQL 長度: 755
2025-07-21 02:26:46.713 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:26:46.947 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_EXAM_UCODE'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:26:47.085 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:26:47.206 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:26:47.208 +08:00 [INF] 正在執行 SQL 語句，長度: 1122
2025-07-21 02:26:47.208 +08:00 [INF] 正在執行查詢，SQL 長度: 1122
2025-07-21 02:26:47.208 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:26:47.274 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:26:47.274 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 66 毫秒
2025-07-21 02:26:49.074 +08:00 [INF] 正在取得檢視表定義: TO_INDEX1
2025-07-21 02:26:49.348 +08:00 [INF] 成功取得檢視表定義: TO_INDEX1
2025-07-21 02:26:49.349 +08:00 [INF] 正在執行 SQL 語句，長度: 751
2025-07-21 02:26:49.349 +08:00 [INF] 正在執行查詢，SQL 長度: 751
2025-07-21 02:26:49.349 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:26:49.543 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_INDEX1'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:26:49.638 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:26:49.773 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:26:49.775 +08:00 [INF] 正在執行 SQL 語句，長度: 1114
2025-07-21 02:26:49.775 +08:00 [INF] 正在執行查詢，SQL 長度: 1114
2025-07-21 02:26:49.775 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:26:49.788 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:26:49.789 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 13 毫秒
2025-07-21 02:26:51.632 +08:00 [INF] 正在取得檢視表定義: MEMBER_VIEW
2025-07-21 02:26:51.886 +08:00 [INF] 成功取得檢視表定義: MEMBER_VIEW
2025-07-21 02:26:51.887 +08:00 [INF] 正在執行 SQL 語句，長度: 753
2025-07-21 02:26:51.887 +08:00 [INF] 正在執行查詢，SQL 長度: 753
2025-07-21 02:26:51.887 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:26:52.110 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'MEMBER_VIEW'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:26:52.188 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:26:52.319 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:26:52.322 +08:00 [INF] 正在執行 SQL 語句，長度: 1118
2025-07-21 02:26:52.322 +08:00 [INF] 正在執行查詢，SQL 長度: 1118
2025-07-21 02:26:52.322 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:26:52.328 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:26:52.328 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 6 毫秒
2025-07-21 02:26:55.989 +08:00 [INF] 正在取得檢視表定義: TO_MAIN$V2
2025-07-21 02:26:56.263 +08:00 [INF] 成功取得檢視表定義: TO_MAIN$V2
2025-07-21 02:26:56.264 +08:00 [INF] 正在執行 SQL 語句，長度: 752
2025-07-21 02:26:56.264 +08:00 [INF] 正在執行查詢，SQL 長度: 752
2025-07-21 02:26:56.264 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:26:56.539 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_MAIN$V2'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:26:56.628 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:26:56.758 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:26:56.759 +08:00 [INF] 正在執行 SQL 語句，長度: 1116
2025-07-21 02:26:56.759 +08:00 [INF] 正在執行查詢，SQL 長度: 1116
2025-07-21 02:26:56.759 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:26:56.855 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:26:56.855 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 96 毫秒
2025-07-21 02:26:58.598 +08:00 [INF] 正在取得檢視表定義: TO_RECEIVE_V1
2025-07-21 02:26:58.963 +08:00 [INF] 成功取得檢視表定義: TO_RECEIVE_V1
2025-07-21 02:26:58.964 +08:00 [INF] 正在執行 SQL 語句，長度: 755
2025-07-21 02:26:58.964 +08:00 [INF] 正在執行查詢，SQL 長度: 755
2025-07-21 02:26:58.964 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:26:59.238 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_RECEIVE_V1'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:26:59.339 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:26:59.463 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:26:59.464 +08:00 [INF] 正在執行 SQL 語句，長度: 1122
2025-07-21 02:26:59.464 +08:00 [INF] 正在執行查詢，SQL 長度: 1122
2025-07-21 02:26:59.464 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:26:59.551 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:26:59.552 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 87 毫秒
2025-07-21 02:27:00.982 +08:00 [INF] 正在取得檢視表定義: TO_VERSION_INFO$V2
2025-07-21 02:27:01.250 +08:00 [INF] 成功取得檢視表定義: TO_VERSION_INFO$V2
2025-07-21 02:27:01.251 +08:00 [INF] 正在執行 SQL 語句，長度: 760
2025-07-21 02:27:01.251 +08:00 [INF] 正在執行查詢，SQL 長度: 760
2025-07-21 02:27:01.251 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:27:01.552 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_VERSION_INFO$V2'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:27:01.644 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:27:01.774 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:27:01.775 +08:00 [INF] 正在執行 SQL 語句，長度: 1132
2025-07-21 02:27:01.775 +08:00 [INF] 正在執行查詢，SQL 長度: 1132
2025-07-21 02:27:01.775 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:27:01.892 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 02:27:01.892 +08:00 [INF] 查詢執行成功，返回 4 筆資料，耗時 116 毫秒
2025-07-21 02:27:06.442 +08:00 [INF] 正在取得檢視表定義: TO_WORKSHEET_V1
2025-07-21 02:27:06.716 +08:00 [INF] 成功取得檢視表定義: TO_WORKSHEET_V1
2025-07-21 02:27:06.717 +08:00 [INF] 正在執行 SQL 語句，長度: 757
2025-07-21 02:27:06.717 +08:00 [INF] 正在執行查詢，SQL 長度: 757
2025-07-21 02:27:06.717 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:27:06.987 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_WORKSHEET_V1'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:27:07.075 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:27:07.185 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:27:07.187 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 02:27:07.187 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 02:27:07.187 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:27:07.322 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:27:07.322 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 135 毫秒
2025-07-21 02:48:42.703 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:48:42.704 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:48:55.651 +08:00 [DBG] Hosting starting
2025-07-21 02:48:55.709 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:48:55.711 +08:00 [INF] Hosting environment: Production
2025-07-21 02:48:55.711 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:48:55.711 +08:00 [DBG] Hosting started
2025-07-21 02:49:00.112 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:49:00.527 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:49:01.819 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:49:01.820 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:49:03.669 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:49:03.696 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:49:03.696 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:49:04.300 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:49:04.300 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:49:04.315 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:49:04.315 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:49:04.316 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:49:06.849 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:49:06.850 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:49:06.853 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 02:49:06.939 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 02:49:06.940 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 90 毫秒
2025-07-21 02:49:08.057 +08:00 [INF] 正在取得檢視表定義: MEMBER_VIEW
2025-07-21 02:49:08.374 +08:00 [INF] 成功取得檢視表定義: MEMBER_VIEW
2025-07-21 02:49:09.688 +08:00 [INF] 正在執行 SQL 語句，長度: 753
2025-07-21 02:49:09.692 +08:00 [INF] 正在執行查詢，SQL 長度: 753
2025-07-21 02:49:09.693 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:49:09.851 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'MEMBER_VIEW'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:49:09.988 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:49:10.056 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:49:10.059 +08:00 [INF] 正在執行 SQL 語句，長度: 1118
2025-07-21 02:49:10.059 +08:00 [INF] 正在執行查詢，SQL 長度: 1118
2025-07-21 02:49:10.059 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:49:10.069 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:49:10.070 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 10 毫秒
2025-07-21 02:49:12.638 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:49:12.640 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:49:54.840 +08:00 [DBG] Hosting starting
2025-07-21 02:49:54.910 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:49:54.915 +08:00 [INF] Hosting environment: Production
2025-07-21 02:49:54.916 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:49:54.917 +08:00 [DBG] Hosting started
2025-07-21 02:49:57.191 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:49:57.339 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:49:58.490 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:49:58.491 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:50:00.297 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:50:00.356 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:50:00.358 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:50:00.577 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:50:00.577 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:50:00.592 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:50:00.593 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:50:00.593 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:50:03.504 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:50:03.506 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:50:03.510 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:50:03.613 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 02:50:03.614 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 109 毫秒
2025-07-21 02:50:05.429 +08:00 [INF] 正在取得預存程序原始碼: PROCEDURE1
2025-07-21 02:50:05.430 +08:00 [INF] 正在取得物件原始碼: PROCEDURE1, 類型: PROCEDURE
2025-07-21 02:50:05.432 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PROCEDURE1, TYPE=PROCEDURE
2025-07-21 02:50:05.506 +08:00 [INF] 正在取得預存程序原始碼: PROCEDURE1
2025-07-21 02:50:05.506 +08:00 [INF] 正在取得物件原始碼: PROCEDURE1, 類型: PROCEDURE
2025-07-21 02:50:05.507 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PROCEDURE1, TYPE=PROCEDURE
2025-07-21 02:50:05.535 +08:00 [INF] 成功取得物件原始碼: PROCEDURE1, 類型: PROCEDURE, 行數: 9, 內容長度: 111
2025-07-21 02:50:05.535 +08:00 [INF] 成功取得預存程序原始碼: PROCEDURE1
2025-07-21 02:50:07.170 +08:00 [INF] 正在執行 SQL 語句，長度: 731
2025-07-21 02:50:07.172 +08:00 [INF] 正在執行查詢，SQL 長度: 731
2025-07-21 02:50:07.172 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:50:07.183 +08:00 [INF] 成功取得物件原始碼: PROCEDURE1, 類型: PROCEDURE, 行數: 9, 內容長度: 111
2025-07-21 02:50:07.184 +08:00 [INF] 成功取得預存程序原始碼: PROCEDURE1
2025-07-21 02:50:07.711 +08:00 [INF] 正在執行 SQL 語句，長度: 731
2025-07-21 02:50:07.713 +08:00 [INF] 正在執行查詢，SQL 長度: 731
2025-07-21 02:50:07.713 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:50:07.782 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:50:07.782 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 610 毫秒
2025-07-21 02:50:07.785 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 02:50:07.844 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 02:50:07.844 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:50:07.846 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:50:07.846 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 132 毫秒
2025-07-21 02:50:07.846 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 02:50:07.936 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 02:50:07.936 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:50:07.939 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:50:07.940 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 96 毫秒
2025-07-21 02:50:07.991 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:50:07.991 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 54 毫秒
2025-07-21 02:50:09.840 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:50:09.842 +08:00 [INF] 所有活動連線已關閉
2025-07-21 02:56:28.033 +08:00 [DBG] Hosting starting
2025-07-21 02:56:28.064 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 02:56:28.067 +08:00 [INF] Hosting environment: Production
2025-07-21 02:56:28.068 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 02:56:28.068 +08:00 [DBG] Hosting started
2025-07-21 02:56:29.871 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:56:30.038 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:56:31.086 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 02:56:31.086 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 02:56:32.953 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 02:56:32.989 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 02:56:32.989 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 02:56:33.203 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 02:56:33.203 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 02:56:33.218 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 02:56:33.219 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 02:56:33.219 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 02:56:36.100 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:56:36.101 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 02:56:36.103 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:56:36.204 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 02:56:36.206 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 105 毫秒
2025-07-21 02:56:37.604 +08:00 [INF] 正在取得預存程序原始碼: PROCEDURE1
2025-07-21 02:56:37.605 +08:00 [INF] 正在取得物件原始碼: PROCEDURE1, 類型: PROCEDURE
2025-07-21 02:56:37.607 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PROCEDURE1, TYPE=PROCEDURE
2025-07-21 02:56:37.853 +08:00 [INF] 正在取得預存程序原始碼: PROCEDURE1
2025-07-21 02:56:37.853 +08:00 [INF] 正在取得物件原始碼: PROCEDURE1, 類型: PROCEDURE
2025-07-21 02:56:37.853 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PROCEDURE1, TYPE=PROCEDURE
2025-07-21 02:56:37.858 +08:00 [INF] 成功取得物件原始碼: PROCEDURE1, 類型: PROCEDURE, 行數: 9, 內容長度: 111
2025-07-21 02:56:37.859 +08:00 [INF] 成功取得預存程序原始碼: PROCEDURE1
2025-07-21 02:56:39.352 +08:00 [INF] 正在執行 SQL 語句，長度: 731
2025-07-21 02:56:39.354 +08:00 [INF] 正在執行查詢，SQL 長度: 731
2025-07-21 02:56:39.354 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:56:39.366 +08:00 [INF] 成功取得物件原始碼: PROCEDURE1, 類型: PROCEDURE, 行數: 9, 內容長度: 111
2025-07-21 02:56:39.367 +08:00 [INF] 成功取得預存程序原始碼: PROCEDURE1
2025-07-21 02:56:39.981 +08:00 [INF] 正在執行 SQL 語句，長度: 731
2025-07-21 02:56:40.063 +08:00 [INF] 正在執行查詢，SQL 長度: 731
2025-07-21 02:56:40.064 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:56:40.067 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:56:40.068 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 713 毫秒
2025-07-21 02:56:40.071 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 02:56:40.071 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 02:56:40.071 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:56:40.136 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:56:40.136 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 72 毫秒
2025-07-21 02:56:40.136 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 02:56:40.186 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 02:56:40.186 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:56:40.188 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:56:40.188 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 116 毫秒
2025-07-21 02:56:40.241 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 02:56:40.241 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 54 毫秒
2025-07-21 02:56:46.387 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:56:46.388 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 02:56:46.389 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 02:56:46.459 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 02:56:46.459 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 71 毫秒
2025-07-21 02:56:47.905 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 02:56:47.905 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 02:56:47.905 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 02:56:48.099 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 02:56:48.099 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 02:56:48.099 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 02:56:48.103 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 02:56:48.104 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 02:56:48.104 +08:00 [INF] 返回函數原始碼: 長度=152, 內容預覽=FUNCTION Ch_Date
(
DD in Date
)
RETURN VARCHAR2 AS
2025-07-21 02:56:49.202 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 02:56:49.203 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 02:56:49.203 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:56:49.212 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 02:56:49.213 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 02:56:49.213 +08:00 [INF] 返回函數原始碼: 長度=152, 內容預覽=FUNCTION Ch_Date
(
DD in Date
)
RETURN VARCHAR2 AS
2025-07-21 02:56:50.147 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 02:56:50.147 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 02:56:50.147 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 02:56:50.149 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 02:56:50.149 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 946 毫秒
2025-07-21 02:56:50.152 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 02:56:50.162 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 02:56:50.255 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:56:50.257 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 02:56:50.257 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 110 毫秒
2025-07-21 02:56:50.257 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 02:56:50.334 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 02:56:50.334 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:56:50.336 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:56:50.336 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 80 毫秒
2025-07-21 02:56:50.424 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 02:56:50.424 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 90 毫秒
2025-07-21 02:56:55.996 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:56:55.996 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 02:56:55.997 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 02:56:56.016 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 02:56:56.021 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 25 毫秒
2025-07-21 02:56:57.708 +08:00 [INF] 正在取得檢視表定義: TO_INDEX1
2025-07-21 02:56:58.068 +08:00 [INF] 成功取得檢視表定義: TO_INDEX1
2025-07-21 02:56:59.545 +08:00 [INF] 正在執行 SQL 語句，長度: 751
2025-07-21 02:56:59.545 +08:00 [INF] 正在執行查詢，SQL 長度: 751
2025-07-21 02:56:59.545 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        COLUMN_NAME AS Name,
                       ...
2025-07-21 02:56:59.692 +08:00 [ERR] 執行查詢失敗: 
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = 'TO_INDEX1'
                    ORDER BY 
                        c.COLUMN_ID
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
2025-07-21 02:56:59.756 +08:00 [ERR] 查詢執行失敗
OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
2025-07-21 02:56:59.818 +08:00 [ERR] SQL 語句執行失敗
OracleMS.Exceptions.OracleManagementException: 查詢執行失敗: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> OracleMS.Exceptions.OracleManagementException: 執行查詢失敗: ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
 ---> Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00918: 資料欄的定義不明確
https://docs.oracle.com/error-help/db/ora-00918/
   at OracleInternal.ServiceObjects.OracleFailoverMgrImpl.OnError(OracleConnection connection, CallHistoryRecord chr, Object mi, Exception ex, Boolean bTopLevelCall, Boolean& bCanRecordNewCall)
   at Oracle.ManagedDataAccess.Client.OracleDataAdapter.Fill(DataTable dataTable)
   at OracleMS.Repositories.OracleDatabaseRepository.<>c__DisplayClass3_1.<ExecuteQueryAsync>b__0() in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 69
   --- End of inner exception stack trace ---
   at OracleMS.Repositories.OracleDatabaseRepository.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Repositories\OracleDatabaseRepository.cs:line 84
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 119
   --- End of inner exception stack trace ---
   at OracleMS.Services.DatabaseService.ExecuteQueryAsync(IDbConnection connection, String sql) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 136
   at OracleMS.Services.DatabaseService.ExecuteQueryWithCancellationAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 367
   at OracleMS.Services.DatabaseService.ExecuteSqlAsync(IDbConnection connection, String sql, CancellationToken cancellationToken) in F:\Projects\ORMS\OracleMS\OracleMS\Services\DatabaseService.cs:line 315
2025-07-21 02:56:59.820 +08:00 [INF] 正在執行 SQL 語句，長度: 1114
2025-07-21 02:56:59.820 +08:00 [INF] 正在執行查詢，SQL 長度: 1114
2025-07-21 02:56:59.820 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 02:56:59.827 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 02:56:59.827 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 6 毫秒
2025-07-21 02:57:29.384 +08:00 [DBG] 視窗設定已儲存
2025-07-21 02:57:29.386 +08:00 [INF] 所有活動連線已關閉
2025-07-21 03:03:49.529 +08:00 [DBG] Hosting starting
2025-07-21 03:03:49.571 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 03:03:49.574 +08:00 [INF] Hosting environment: Production
2025-07-21 03:03:49.575 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 03:03:49.575 +08:00 [DBG] Hosting started
2025-07-21 03:03:51.556 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:03:51.680 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:03:53.031 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 03:03:53.032 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 03:03:54.989 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 03:03:55.026 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 03:03:55.026 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:03:55.193 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:03:55.194 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 03:03:55.206 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 03:03:55.206 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 03:03:55.206 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 03:03:57.738 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 03:03:57.739 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 03:03:57.741 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:03:57.830 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 03:03:57.832 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 93 毫秒
2025-07-21 03:03:59.738 +08:00 [INF] 正在取得預存程序原始碼: PROCEDURE1
2025-07-21 03:03:59.739 +08:00 [INF] 正在取得物件原始碼: PROCEDURE1, 類型: PROCEDURE
2025-07-21 03:03:59.741 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=PROCEDURE1, TYPE=PROCEDURE
2025-07-21 03:03:59.767 +08:00 [INF] 成功取得物件原始碼: PROCEDURE1, 類型: PROCEDURE, 行數: 9, 內容長度: 111
2025-07-21 03:03:59.768 +08:00 [INF] 成功取得預存程序原始碼: PROCEDURE1
2025-07-21 03:04:01.106 +08:00 [INF] 正在執行 SQL 語句，長度: 731
2025-07-21 03:04:01.107 +08:00 [INF] 正在執行查詢，SQL 長度: 731
2025-07-21 03:04:01.108 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 03:04:01.181 +08:00 [INF] 查詢執行成功，返回 0 筆資料
2025-07-21 03:04:01.181 +08:00 [INF] 查詢執行成功，返回 0 筆資料，耗時 73 毫秒
2025-07-21 03:04:01.183 +08:00 [INF] 正在執行 SQL 語句，長度: 1136
2025-07-21 03:04:01.183 +08:00 [INF] 正在執行查詢，SQL 長度: 1136
2025-07-21 03:04:01.183 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 03:04:01.242 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 03:04:01.242 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 58 毫秒
2025-07-21 03:04:05.897 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:04:05.897 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:04:05.898 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:04:05.906 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 03:04:05.907 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 9 毫秒
2025-07-21 03:04:07.023 +08:00 [INF] 正在取得函數原始碼: CH_DATE
2025-07-21 03:04:07.023 +08:00 [INF] 正在取得物件原始碼: CH_DATE, 類型: FUNCTION
2025-07-21 03:04:07.023 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=CH_DATE, TYPE=FUNCTION
2025-07-21 03:04:07.038 +08:00 [INF] 成功取得物件原始碼: CH_DATE, 類型: FUNCTION, 行數: 12, 內容長度: 152
2025-07-21 03:04:07.038 +08:00 [INF] 成功取得函數原始碼: CH_DATE
2025-07-21 03:04:07.038 +08:00 [INF] 返回函數原始碼: 長度=152, 內容預覽=FUNCTION Ch_Date
(
DD in Date
)
RETURN VARCHAR2 AS
2025-07-21 03:04:10.999 +08:00 [INF] 正在執行 SQL 語句，長度: 720
2025-07-21 03:04:10.999 +08:00 [INF] 正在執行查詢，SQL 長度: 720
2025-07-21 03:04:10.999 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        ARGUMENT_NAME AS Name,
                     ...
2025-07-21 03:04:11.081 +08:00 [INF] 查詢執行成功，返回 2 筆資料
2025-07-21 03:04:11.081 +08:00 [INF] 查詢執行成功，返回 2 筆資料，耗時 82 毫秒
2025-07-21 03:04:11.084 +08:00 [INF] 正在執行 SQL 語句，長度: 1126
2025-07-21 03:04:11.084 +08:00 [INF] 正在執行查詢，SQL 長度: 1126
2025-07-21 03:04:11.084 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 03:04:11.145 +08:00 [INF] 查詢執行成功，返回 1 筆資料
2025-07-21 03:04:11.145 +08:00 [INF] 查詢執行成功，返回 1 筆資料，耗時 60 毫秒
2025-07-21 03:04:18.522 +08:00 [INF] 正在取得資料庫物件清單，類型: "Sequence"
2025-07-21 03:04:18.522 +08:00 [INF] 正在取得資料庫物件清單，類型: "Sequence"
2025-07-21 03:04:18.523 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:04:18.594 +08:00 [INF] 查詢執行成功，返回 47 筆資料
2025-07-21 03:04:18.594 +08:00 [INF] 成功取得 47 個 "Sequence" 物件，耗時 71 毫秒
2025-07-21 03:04:20.362 +08:00 [INF] 正在取得序列定義: EVT_NOTIFY_SEQ
2025-07-21 03:04:20.434 +08:00 [ERR] 取得序列定義失敗: EVT_NOTIFY_SEQ
System.OverflowException: Value was either too large or too small for an Int64.
   at System.Decimal.ToInt64(Decimal d)
   at System.Decimal.System.IConvertible.ToInt64(IFormatProvider provider)
   at OracleMS.Services.ObjectEditorService.GetSequenceDefinitionAsync(IDbConnection connection, String sequenceName) in F:\Projects\ORMS\OracleMS\OracleMS\Services\ObjectEditorService.cs:line 2028
2025-07-21 03:04:28.713 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:04:28.713 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:04:28.714 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:04:28.852 +08:00 [INF] 查詢執行成功，返回 226 筆資料
2025-07-21 03:04:28.853 +08:00 [INF] 成功取得 226 個 "Index" 物件，耗時 139 毫秒
2025-07-21 03:04:30.558 +08:00 [INF] 正在取得索引定義: EIS_1_NSN
2025-07-21 03:04:30.580 +08:00 [INF] 成功取得索引定義: EIS_1_NSN
2025-07-21 03:04:35.568 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:04:35.568 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:04:35.568 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:04:35.591 +08:00 [INF] 查詢執行成功，返回 55 筆資料
2025-07-21 03:04:35.591 +08:00 [INF] 成功取得 55 個 "Trigger" 物件，耗時 23 毫秒
2025-07-21 03:04:36.523 +08:00 [INF] 正在取得觸發器定義: EIS_LTC_BINS
2025-07-21 03:04:37.131 +08:00 [INF] 成功取得觸發器定義: EIS_LTC_BINS
2025-07-21 03:04:37.151 +08:00 [INF] 正在執行 SQL 語句，長度: 1073
2025-07-21 03:04:37.151 +08:00 [INF] 正在執行查詢，SQL 長度: 1073
2025-07-21 03:04:37.151 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 03:04:37.397 +08:00 [INF] 查詢執行成功，返回 3 筆資料
2025-07-21 03:04:37.397 +08:00 [INF] 查詢執行成功，返回 3 筆資料，耗時 246 毫秒
2025-07-21 03:04:52.523 +08:00 [INF] 正在取得觸發器定義: TOB_AU1_BINS
2025-07-21 03:04:52.537 +08:00 [INF] 成功取得觸發器定義: TOB_AU1_BINS
2025-07-21 03:04:52.544 +08:00 [INF] 正在執行 SQL 語句，長度: 1073
2025-07-21 03:04:52.544 +08:00 [INF] 正在執行查詢，SQL 長度: 1073
2025-07-21 03:04:52.544 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 03:04:52.785 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 03:04:52.785 +08:00 [INF] 查詢執行成功，返回 4 筆資料，耗時 241 毫秒
2025-07-21 03:05:00.187 +08:00 [DBG] 視窗設定已儲存
2025-07-21 03:05:00.188 +08:00 [INF] 所有活動連線已關閉
2025-07-21 03:08:56.070 +08:00 [DBG] Hosting starting
2025-07-21 03:08:56.098 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 03:08:56.101 +08:00 [INF] Hosting environment: Production
2025-07-21 03:08:56.101 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 03:08:56.101 +08:00 [DBG] Hosting started
2025-07-21 03:09:14.479 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:09:14.622 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:09:15.815 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 03:09:15.816 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 03:09:17.380 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 03:09:17.406 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 03:09:17.406 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:09:17.583 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:09:17.584 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 03:09:17.599 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 03:09:17.600 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 03:09:17.600 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 03:09:19.888 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:09:19.889 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:09:19.891 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:09:20.004 +08:00 [INF] 查詢執行成功，返回 55 筆資料
2025-07-21 03:09:20.005 +08:00 [INF] 成功取得 55 個 "Trigger" 物件，耗時 116 毫秒
2025-07-21 03:09:21.697 +08:00 [INF] 正在取得觸發器定義: TOB_APPL_1_BINS
2025-07-21 03:09:22.295 +08:00 [INF] 成功取得觸發器定義: TOB_APPL_1_BINS
2025-07-21 03:09:22.322 +08:00 [INF] 正在執行 SQL 語句，長度: 1079
2025-07-21 03:09:22.323 +08:00 [INF] 正在執行查詢，SQL 長度: 1079
2025-07-21 03:09:22.323 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 03:09:22.556 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 03:09:22.556 +08:00 [INF] 查詢執行成功，返回 4 筆資料，耗時 232 毫秒
2025-07-21 03:09:34.486 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:09:34.486 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:09:34.486 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:09:34.512 +08:00 [INF] 查詢執行成功，返回 226 筆資料
2025-07-21 03:09:34.514 +08:00 [INF] 成功取得 226 個 "Index" 物件，耗時 28 毫秒
2025-07-21 03:09:35.920 +08:00 [INF] 正在取得索引定義: IDX_ITEM
2025-07-21 03:09:35.941 +08:00 [INF] 成功取得索引定義: IDX_ITEM
2025-07-21 03:09:38.237 +08:00 [DBG] 視窗設定已儲存
2025-07-21 03:09:38.239 +08:00 [INF] 所有活動連線已關閉
2025-07-21 03:13:15.458 +08:00 [DBG] Hosting starting
2025-07-21 03:13:15.528 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 03:13:15.534 +08:00 [INF] Hosting environment: Production
2025-07-21 03:13:15.535 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 03:13:15.535 +08:00 [DBG] Hosting started
2025-07-21 03:13:18.318 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:13:18.484 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:13:19.947 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 03:13:19.949 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 03:13:21.841 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 03:13:21.867 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 03:13:21.867 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:13:22.028 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:13:22.028 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 03:13:22.041 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 03:13:22.041 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 03:13:22.041 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 03:13:25.694 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:13:25.696 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:13:25.698 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:13:25.782 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 03:13:25.783 +08:00 [INF] 成功取得 4 個 "Package" 物件，耗時 88 毫秒
2025-07-21 03:13:28.066 +08:00 [INF] 開始載入套件定義: TO_EIS
2025-07-21 03:13:28.067 +08:00 [INF] 正在取得套件定義: TO_EIS
2025-07-21 03:13:28.068 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE
2025-07-21 03:13:28.070 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE
2025-07-21 03:13:28.115 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE, 行數: 72, 內容長度: 1688
2025-07-21 03:13:28.115 +08:00 [INF] 正在取得物件原始碼: TO_EIS, 類型: PACKAGE BODY
2025-07-21 03:13:28.115 +08:00 [DBG] 執行 SQL: 
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE, 參數: NAME=TO_EIS, TYPE=PACKAGE BODY
2025-07-21 03:13:28.194 +08:00 [INF] 成功取得物件原始碼: TO_EIS, 類型: PACKAGE BODY, 行數: 313, 內容長度: 10149
2025-07-21 03:13:28.204 +08:00 [INF] 成功取得套件定義: TO_EIS
2025-07-21 03:13:31.390 +08:00 [INF] 從服務取得套件定義: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 03:13:31.391 +08:00 [INF] PackageDefinition 屬性已設置: TO_EIS, 規格長度: 1688, 主體長度: 10149
2025-07-21 03:13:31.391 +08:00 [INF] 開始設置原始碼內容，_isInitializing = true
2025-07-21 03:13:31.392 +08:00 [INF] 準備設置原始碼內容 - 規格長度: 1688, 主體長度: 10149
2025-07-21 03:13:31.408 +08:00 [INF] 規格原始碼已設置，實際長度: 1688
2025-07-21 03:13:31.411 +08:00 [INF] 主體原始碼已設置，實際長度: 10149
2025-07-21 03:13:31.411 +08:00 [INF] 原始碼內容設置完成，_isInitializing = false
2025-07-21 03:13:31.415 +08:00 [INF] 正在執行 SQL 語句，長度: 1120
2025-07-21 03:13:31.416 +08:00 [INF] 正在執行查詢，SQL 長度: 1120
2025-07-21 03:13:31.416 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 03:13:31.497 +08:00 [INF] 查詢執行成功，返回 9 筆資料
2025-07-21 03:13:31.497 +08:00 [INF] 查詢執行成功，返回 9 筆資料，耗時 81 毫秒
2025-07-21 03:13:37.318 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:13:37.318 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:13:37.319 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:13:37.342 +08:00 [INF] 查詢執行成功，返回 55 筆資料
2025-07-21 03:13:37.343 +08:00 [INF] 成功取得 55 個 "Trigger" 物件，耗時 24 毫秒
2025-07-21 03:13:39.604 +08:00 [INF] 正在取得觸發器定義: TOB_APPL_1_DEL
2025-07-21 03:13:40.071 +08:00 [INF] 成功取得觸發器定義: TOB_APPL_1_DEL
2025-07-21 03:13:40.088 +08:00 [INF] 正在執行 SQL 語句，長度: 1077
2025-07-21 03:13:40.088 +08:00 [INF] 正在執行查詢，SQL 長度: 1077
2025-07-21 03:13:40.088 +08:00 [INF] 正在執行查詢: 
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                 ...
2025-07-21 03:13:40.315 +08:00 [INF] 查詢執行成功，返回 5 筆資料
2025-07-21 03:13:40.315 +08:00 [INF] 查詢執行成功，返回 5 筆資料，耗時 227 毫秒
2025-07-21 03:13:59.605 +08:00 [DBG] 視窗設定已儲存
2025-07-21 03:13:59.605 +08:00 [INF] 所有活動連線已關閉
2025-07-21 03:16:47.373 +08:00 [DBG] Hosting starting
2025-07-21 03:16:47.435 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 03:16:47.441 +08:00 [INF] Hosting environment: Production
2025-07-21 03:16:47.443 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 03:16:47.443 +08:00 [DBG] Hosting started
2025-07-21 03:16:49.256 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:16:49.401 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:16:50.365 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 03:16:50.366 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 03:16:52.386 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 03:16:52.416 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 03:16:52.417 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:16:52.597 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:16:52.598 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 03:16:52.611 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 03:16:52.612 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 03:16:52.612 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 03:16:55.036 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:16:55.037 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:16:55.039 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:16:55.143 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 03:16:55.146 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 109 毫秒
2025-07-21 03:17:32.942 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:17:32.942 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:17:32.943 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:17:32.953 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:17:32.965 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:17:32.965 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:17:32.981 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 03:17:32.981 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 03:17:32.982 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:17:32.990 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:17:32.991 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:17:32.992 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:17:33.023 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:17:33.023 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:17:33.024 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:17:33.041 +08:00 [INF] 正在取得資料庫物件清單，類型: "Sequence"
2025-07-21 03:17:33.041 +08:00 [INF] 正在取得資料庫物件清單，類型: "Sequence"
2025-07-21 03:17:33.042 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:17:33.056 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:17:33.061 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:17:33.062 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:17:33.076 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:17:33.079 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:17:33.080 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:17:33.082 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 03:17:33.082 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 03:17:33.083 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 03:17:33.083 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 03:17:33.083 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 03:17:33.083 +08:00 [INF] 查詢執行成功，返回 47 筆資料
2025-07-21 03:17:33.083 +08:00 [INF] 查詢執行成功，返回 55 筆資料
2025-07-21 03:17:33.084 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 142 毫秒
2025-07-21 03:17:33.084 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 119 毫秒
2025-07-21 03:17:33.084 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 103 毫秒
2025-07-21 03:17:33.084 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 93 毫秒
2025-07-21 03:17:33.084 +08:00 [INF] 成功取得 4 個 "Package" 物件，耗時 61 毫秒
2025-07-21 03:17:33.084 +08:00 [INF] 成功取得 47 個 "Sequence" 物件，耗時 43 毫秒
2025-07-21 03:17:33.084 +08:00 [INF] 成功取得 55 個 "Trigger" 物件，耗時 24 毫秒
2025-07-21 03:17:33.359 +08:00 [INF] 查詢執行成功，返回 226 筆資料
2025-07-21 03:17:33.361 +08:00 [INF] 成功取得 226 個 "Index" 物件，耗時 281 毫秒
2025-07-21 03:20:07.828 +08:00 [DBG] 視窗設定已儲存
2025-07-21 03:20:07.831 +08:00 [INF] 所有活動連線已關閉
2025-07-21 03:21:23.167 +08:00 [DBG] Hosting starting
2025-07-21 03:21:23.224 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 03:21:23.227 +08:00 [INF] Hosting environment: Production
2025-07-21 03:21:23.228 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 03:21:23.228 +08:00 [DBG] Hosting started
2025-07-21 03:21:28.821 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:21:28.954 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:21:29.909 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 03:21:29.910 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 03:21:31.624 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 03:21:31.684 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 03:21:31.684 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:21:31.848 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:21:31.848 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 03:21:31.861 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 03:21:31.861 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 03:21:31.861 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 03:21:36.205 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:21:36.206 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:21:36.208 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:21:36.219 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:21:36.220 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:21:36.220 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:21:36.234 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:21:36.234 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:21:36.235 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:21:36.245 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 03:21:36.245 +08:00 [INF] 正在取得資料庫物件清單，類型: "Procedure"
2025-07-21 03:21:36.246 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:21:36.260 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:21:36.260 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:21:36.261 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:21:36.279 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:21:36.280 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:21:36.281 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:21:36.299 +08:00 [INF] 正在取得資料庫物件清單，類型: "Sequence"
2025-07-21 03:21:36.299 +08:00 [INF] 正在取得資料庫物件清單，類型: "Sequence"
2025-07-21 03:21:36.300 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:21:36.319 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:21:36.320 +08:00 [INF] 正在取得資料庫物件清單，類型: "Trigger"
2025-07-21 03:21:36.320 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:21:36.337 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:21:36.422 +08:00 [INF] 正在取得資料庫物件清單，類型: "Index"
2025-07-21 03:21:36.423 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:21:36.437 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 03:21:36.438 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 03:21:36.438 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 03:21:36.438 +08:00 [INF] 查詢執行成功，返回 38 筆資料
2025-07-21 03:21:36.440 +08:00 [INF] 成功取得 4 個 "Package" 物件，耗時 160 毫秒
2025-07-21 03:21:36.440 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 179 毫秒
2025-07-21 03:21:36.441 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 206 毫秒
2025-07-21 03:21:36.441 +08:00 [INF] 成功取得 38 個 "Procedure" 物件，耗時 195 毫秒
2025-07-21 03:21:36.515 +08:00 [INF] 查詢執行成功，返回 55 筆資料
2025-07-21 03:21:36.515 +08:00 [INF] 查詢執行成功，返回 47 筆資料
2025-07-21 03:21:36.515 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 03:21:36.515 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 03:21:36.515 +08:00 [INF] 查詢執行成功，返回 226 筆資料
2025-07-21 03:21:36.518 +08:00 [INF] 成功取得 55 個 "Trigger" 物件，耗時 198 毫秒
2025-07-21 03:21:36.518 +08:00 [INF] 成功取得 47 個 "Sequence" 物件，耗時 219 毫秒
2025-07-21 03:21:36.518 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 313 毫秒
2025-07-21 03:21:36.518 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 298 毫秒
2025-07-21 03:21:36.518 +08:00 [INF] 成功取得 226 個 "Index" 物件，耗時 96 毫秒
2025-07-21 03:22:48.846 +08:00 [DBG] 視窗設定已儲存
2025-07-21 03:22:48.847 +08:00 [INF] 所有活動連線已關閉
2025-07-21 03:24:46.252 +08:00 [DBG] Hosting starting
2025-07-21 03:24:46.287 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 03:24:46.290 +08:00 [INF] Hosting environment: Production
2025-07-21 03:24:46.291 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 03:24:46.291 +08:00 [DBG] Hosting started
2025-07-21 03:24:48.303 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:24:48.432 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:24:49.542 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 03:24:49.543 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 03:24:51.322 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 03:24:51.348 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 03:24:51.348 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 03:24:51.527 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 03:24:51.528 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 03:24:51.539 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 03:24:51.539 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 03:24:51.539 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 03:24:53.886 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:24:53.889 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 03:24:53.892 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:24:54.371 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 03:24:54.374 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 487 毫秒
2025-07-21 03:24:58.422 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:24:58.422 +08:00 [INF] 正在取得資料庫物件清單，類型: "View"
2025-07-21 03:24:58.424 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 03:24:58.442 +08:00 [INF] 查詢執行成功，返回 16 筆資料
2025-07-21 03:24:58.443 +08:00 [INF] 成功取得 16 個 "View" 物件，耗時 20 毫秒
2025-07-21 03:25:02.257 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:25:02.257 +08:00 [INF] 正在取得資料庫物件清單，類型: "Function"
2025-07-21 03:25:02.258 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:25:02.268 +08:00 [INF] 查詢執行成功，返回 12 筆資料
2025-07-21 03:25:02.269 +08:00 [INF] 成功取得 12 個 "Function" 物件，耗時 11 毫秒
2025-07-21 03:25:04.369 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:25:04.369 +08:00 [INF] 正在取得資料庫物件清單，類型: "Package"
2025-07-21 03:25:04.370 +08:00 [INF] 正在執行查詢: 
            SELECT 
                object_name as Name,
                owner as Owner,
      ...
2025-07-21 03:25:04.384 +08:00 [INF] 查詢執行成功，返回 4 筆資料
2025-07-21 03:25:04.385 +08:00 [INF] 成功取得 4 個 "Package" 物件，耗時 15 毫秒
2025-07-21 03:25:16.037 +08:00 [INF] 正在取得資料表定義: TO_RMUSER
2025-07-21 03:25:16.040 +08:00 [INF] 正在取得資料表結構: TO_RMUSER
2025-07-21 03:25:17.050 +08:00 [INF] 資料表結構取得成功: TO_RMUSER
2025-07-21 03:25:17.800 +08:00 [INF] 成功取得資料表定義: TO_RMUSER
2025-07-21 03:25:17.804 +08:00 [INF] 正在產生 CREATE 腳本，物件: TO_RMUSER，類型: "Table"
2025-07-21 03:25:17.805 +08:00 [INF] 正在取得資料表結構: TO_RMUSER
2025-07-21 03:25:17.982 +08:00 [INF] 資料表結構取得成功: TO_RMUSER
2025-07-21 03:25:17.985 +08:00 [INF] CREATE 腳本產生成功，物件: TO_RMUSER，腳本長度: 344
2025-07-21 03:25:39.864 +08:00 [INF] 正在產生 CREATE 腳本，物件: TO_RMUSER，類型: "Table"
2025-07-21 03:25:39.864 +08:00 [INF] 正在取得資料表結構: TO_RMUSER
2025-07-21 03:25:40.379 +08:00 [INF] 資料表結構取得成功: TO_RMUSER
2025-07-21 03:25:40.379 +08:00 [INF] CREATE 腳本產生成功，物件: TO_RMUSER，腳本長度: 344
2025-07-21 03:25:51.371 +08:00 [DBG] 視窗設定已儲存
2025-07-21 03:25:51.372 +08:00 [INF] 所有活動連線已關閉
2025-07-21 23:37:26.743 +08:00 [DBG] [] [] Hosting starting
{"EventId":{"Id":1,"Name":"Starting"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host"}
2025-07-21 23:37:26.922 +08:00 [INF] [] [] Application started. Press Ctrl+C to shut down.
{"SourceContext":"Microsoft.Hosting.Lifetime"}
2025-07-21 23:37:26.930 +08:00 [INF] [] [] Hosting environment: Production
{"SourceContext":"Microsoft.Hosting.Lifetime"}
2025-07-21 23:37:26.930 +08:00 [INF] [] [] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
{"SourceContext":"Microsoft.Hosting.Lifetime"}
2025-07-21 23:37:26.930 +08:00 [DBG] [] [] Hosting started
{"EventId":{"Id":2,"Name":"Started"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host"}
2025-07-21 23:37:26.934 +08:00 [INF] [] [] Global exception handling has been set up
{"SourceContext":"OracleMS.App"}
2025-07-21 23:37:29.681 +08:00 [DBG] [] [] 讀取已儲存的連線清單
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:37:29.873 +08:00 [INF] [] [] 成功讀取 1 個已儲存的連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:37:31.343 +08:00 [INF] [] [] 正在建立 Oracle 連線: 新連線
{"SourceContext":"OracleMS.Repositories.OracleConnectionProvider"}
2025-07-21 23:37:31.345 +08:00 [DBG] [] [] 建構連線字串完成，類型: "Basic"
{"SourceContext":"OracleMS.Repositories.OracleConnectionProvider"}
2025-07-21 23:37:33.160 +08:00 [INF] [] [] Oracle 連線建立成功: 新連線
{"SourceContext":"OracleMS.Repositories.OracleConnectionProvider"}
2025-07-21 23:37:33.272 +08:00 [INF] [] [] 儲存連線資訊: 新連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:37:33.272 +08:00 [DBG] [] [] 讀取已儲存的連線清單
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:41:17.598 +08:00 [DBG] Hosting starting
2025-07-21 23:41:17.624 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 23:41:17.626 +08:00 [INF] Hosting environment: Production
2025-07-21 23:41:17.627 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 23:41:17.627 +08:00 [DBG] Hosting started
2025-07-21 23:41:21.176 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:41:21.322 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:41:22.363 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 23:41:22.364 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 23:41:24.634 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 23:41:24.670 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 23:41:24.670 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:41:24.942 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:41:24.943 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 23:41:24.960 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 23:41:24.960 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 23:41:24.960 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 23:41:29.083 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:41:29.085 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:41:29.091 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 23:41:29.356 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 23:41:29.358 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 274 毫秒
2025-07-21 23:41:33.069 +08:00 [INF] 正在取得資料表定義: EIS_1
2025-07-21 23:41:33.071 +08:00 [INF] 正在取得資料表結構: EIS_1
2025-07-21 23:41:33.815 +08:00 [INF] 資料表結構取得成功: EIS_1
2025-07-21 23:41:33.889 +08:00 [INF] 成功取得資料表定義: EIS_1
2025-07-21 23:41:33.894 +08:00 [INF] 正在產生 CREATE 腳本，物件: EIS_1，類型: "Table"
2025-07-21 23:41:33.895 +08:00 [INF] 正在取得資料表結構: EIS_1
2025-07-21 23:41:33.987 +08:00 [INF] 資料表結構取得成功: EIS_1
2025-07-21 23:41:33.988 +08:00 [INF] CREATE 腳本產生成功，物件: EIS_1，腳本長度: 158
2025-07-21 23:41:33.999 +08:00 [DBG] 已載入編輯器狀態: "Table" EIS_1
2025-07-21 23:41:42.146 +08:00 [DBG] 視窗設定已儲存
2025-07-21 23:41:42.148 +08:00 [INF] 所有活動連線已關閉
2025-07-21 23:51:09.068 +08:00 [DBG] Hosting starting
2025-07-21 23:51:09.090 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 23:51:09.093 +08:00 [INF] Hosting environment: Production
2025-07-21 23:51:09.093 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 23:51:09.094 +08:00 [DBG] Hosting started
2025-07-21 23:52:22.772 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:52:22.956 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:52:24.281 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 23:52:24.575 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 23:52:26.331 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 23:52:26.358 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 23:52:26.358 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:52:26.554 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:52:26.554 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 23:52:26.566 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 23:52:26.566 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 23:52:26.567 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 23:52:29.679 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:52:29.681 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:52:29.685 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 23:52:29.831 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 23:52:29.835 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 155 毫秒
2025-07-21 23:52:32.652 +08:00 [INF] 正在取得資料表定義: EIS_AC_AH
2025-07-21 23:52:32.653 +08:00 [INF] 正在取得資料表結構: EIS_AC_AH
2025-07-21 23:52:33.718 +08:00 [INF] 資料表結構取得成功: EIS_AC_AH
2025-07-21 23:52:35.039 +08:00 [INF] 成功取得資料表定義: EIS_AC_AH
2025-07-21 23:52:35.046 +08:00 [INF] 正在產生 CREATE 腳本，物件: EIS_AC_AH，類型: "Table"
2025-07-21 23:52:35.048 +08:00 [INF] 正在取得資料表結構: EIS_AC_AH
2025-07-21 23:52:35.219 +08:00 [INF] 資料表結構取得成功: EIS_AC_AH
2025-07-21 23:52:35.223 +08:00 [INF] CREATE 腳本產生成功，物件: EIS_AC_AH，腳本長度: 226
2025-07-21 23:52:44.863 +08:00 [DBG] 視窗設定已儲存
2025-07-21 23:52:44.864 +08:00 [INF] 所有活動連線已關閉
2025-07-21 23:57:21.769 +08:00 [DBG] Hosting starting
2025-07-21 23:57:21.799 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 23:57:21.802 +08:00 [INF] Hosting environment: Production
2025-07-21 23:57:21.802 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\OracleMS\bin\Debug\net8.0-windows
2025-07-21 23:57:21.803 +08:00 [DBG] Hosting started
2025-07-21 23:57:25.204 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:57:25.343 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:57:26.834 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 23:57:26.835 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 23:57:28.743 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 23:57:28.769 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 23:57:28.769 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:57:28.984 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:57:28.985 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 23:57:29.007 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 23:57:29.008 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 23:57:29.008 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 23:57:31.144 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:57:31.145 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:57:31.147 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 23:57:31.367 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 23:57:31.372 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 227 毫秒
2025-07-21 23:57:32.384 +08:00 [INF] 正在取得資料表定義: EIS_34A
2025-07-21 23:57:32.384 +08:00 [INF] 正在取得資料表結構: EIS_34A
2025-07-21 23:57:32.483 +08:00 [INF] 資料表結構取得成功: EIS_34A
2025-07-21 23:57:32.536 +08:00 [INF] 成功取得資料表定義: EIS_34A
2025-07-21 23:57:32.542 +08:00 [INF] 正在產生 CREATE 腳本，物件: EIS_34A，類型: "Table"
2025-07-21 23:57:32.543 +08:00 [INF] 正在取得資料表結構: EIS_34A
2025-07-21 23:57:32.636 +08:00 [INF] 資料表結構取得成功: EIS_34A
2025-07-21 23:57:32.638 +08:00 [INF] CREATE 腳本產生成功，物件: EIS_34A，腳本長度: 195
2025-07-21 23:57:32.646 +08:00 [DBG] 已載入編輯器狀態: "Table" EIS_34A
2025-07-21 23:57:39.417 +08:00 [DBG] 視窗設定已儲存
2025-07-21 23:57:39.419 +08:00 [INF] 所有活動連線已關閉
