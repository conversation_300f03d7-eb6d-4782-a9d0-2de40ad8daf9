2025-07-21 19:45:40.322 +08:00 [DBG] Hosting starting
2025-07-21 19:45:40.360 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 19:45:40.362 +08:00 [INF] Hosting environment: Production
2025-07-21 19:45:40.363 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\oraclems
2025-07-21 19:45:40.363 +08:00 [DBG] Hosting started
2025-07-21 19:48:02.965 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 19:48:03.171 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 19:48:05.278 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 19:48:05.279 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 19:48:20.535 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\oraclems\Repositories\OracleConnectionProvider.cs:line 48
2025-07-21 19:49:17.752 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 19:49:17.789 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 19:49:28.173 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 19:49:28.173 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 19:49:43.178 +08:00 [ERR] Oracle 連線失敗: 新連線, 錯誤代碼: 50000
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-50000: Connection request timed out
   at OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassw, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch, Boolean bAsync)
   at OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, OracleOpaqueString opaquePassw, OracleOpaqueString securedProxyPassw, OracleConnection connRefForCriteria, Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.OpenInternal(Boolean bAsync)
   at OracleInternal.ServiceObjects.OracleConnectionInternal.Open()
   at Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   at OracleMS.Repositories.OracleConnectionProvider.CreateConnectionAsync(ConnectionInfo connectionInfo) in F:\Projects\ORMS\OracleMS\oraclems\Repositories\OracleConnectionProvider.cs:line 48
2025-07-21 19:49:56.865 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 19:49:56.865 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 19:49:58.045 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 19:49:58.056 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 19:49:58.056 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 19:49:58.512 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 19:49:58.512 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 19:49:58.521 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 19:49:58.521 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 19:49:58.522 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 19:50:01.356 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 19:50:01.357 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 19:50:01.358 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 19:50:01.619 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 19:50:01.621 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 264 毫秒
2025-07-21 19:50:38.868 +08:00 [DBG] Hosting starting
2025-07-21 19:50:38.894 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 19:50:38.896 +08:00 [INF] Hosting environment: Production
2025-07-21 19:50:38.897 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\oraclems
2025-07-21 19:50:38.897 +08:00 [DBG] Hosting started
2025-07-21 19:50:52.004 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 19:50:52.093 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 19:50:53.037 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 19:50:53.038 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 19:50:54.470 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 19:50:54.481 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 19:50:54.481 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 19:50:54.585 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 19:50:54.585 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 19:50:54.594 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 19:50:54.594 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 19:50:54.594 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 19:50:58.360 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 19:50:58.361 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 19:50:58.362 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 19:50:58.483 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 19:50:58.485 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 124 毫秒
2025-07-21 19:51:00.230 +08:00 [INF] 正在取得資料表定義: EIS_1
2025-07-21 19:51:00.231 +08:00 [INF] 正在取得資料表結構: EIS_1
2025-07-21 19:51:03.541 +08:00 [INF] 資料表結構取得成功: EIS_1
2025-07-21 19:51:03.953 +08:00 [INF] 成功取得資料表定義: EIS_1
2025-07-21 19:51:03.958 +08:00 [INF] 正在產生 CREATE 腳本，物件: EIS_1，類型: "Table"
2025-07-21 19:51:03.959 +08:00 [INF] 正在取得資料表結構: EIS_1
2025-07-21 19:51:04.026 +08:00 [INF] 資料表結構取得成功: EIS_1
2025-07-21 19:51:04.028 +08:00 [INF] CREATE 腳本產生成功，物件: EIS_1，腳本長度: 158
2025-07-21 19:51:04.045 +08:00 [DBG] 已載入編輯器狀態: "Table" EIS_1
2025-07-21 19:51:13.403 +08:00 [DBG] 視窗設定已儲存
2025-07-21 19:51:13.404 +08:00 [INF] 所有活動連線已關閉
2025-07-21 23:36:15.771 +08:00 [DBG] [] [] Hosting starting
{"EventId":{"Id":1,"Name":"Starting"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host"}
2025-07-21 23:36:15.797 +08:00 [INF] [] [] Application started. Press Ctrl+C to shut down.
{"SourceContext":"Microsoft.Hosting.Lifetime"}
2025-07-21 23:36:15.800 +08:00 [INF] [] [] Hosting environment: Production
{"SourceContext":"Microsoft.Hosting.Lifetime"}
2025-07-21 23:36:15.800 +08:00 [INF] [] [] Content root path: F:\Projects\ORMS\OracleMS\oraclems
{"SourceContext":"Microsoft.Hosting.Lifetime"}
2025-07-21 23:36:15.800 +08:00 [DBG] [] [] Hosting started
{"EventId":{"Id":2,"Name":"Started"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host"}
2025-07-21 23:36:15.805 +08:00 [INF] [] [] Global exception handling has been set up
{"SourceContext":"OracleMS.App"}
2025-07-21 23:36:27.355 +08:00 [DBG] [] [] 讀取已儲存的連線清單
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:27.457 +08:00 [INF] [] [] 成功讀取 1 個已儲存的連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:28.590 +08:00 [INF] [] [] 正在建立 Oracle 連線: 新連線
{"SourceContext":"OracleMS.Repositories.OracleConnectionProvider"}
2025-07-21 23:36:28.591 +08:00 [DBG] [] [] 建構連線字串完成，類型: "Basic"
{"SourceContext":"OracleMS.Repositories.OracleConnectionProvider"}
2025-07-21 23:36:29.896 +08:00 [INF] [] [] Oracle 連線建立成功: 新連線
{"SourceContext":"OracleMS.Repositories.OracleConnectionProvider"}
2025-07-21 23:36:29.911 +08:00 [INF] [] [] 儲存連線資訊: 新連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:29.911 +08:00 [DBG] [] [] 讀取已儲存的連線清單
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:30.005 +08:00 [INF] [] [] 成功讀取 1 個已儲存的連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:30.006 +08:00 [INF] [] [] 更新現有連線: 新連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:30.018 +08:00 [DBG] [] [] 設定已儲存: SavedConnections
{"SourceContext":"OracleMS.Services.ConfigurationService"}
2025-07-21 23:36:30.018 +08:00 [DBG] [] [] 連線清單儲存成功，共 1 個連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:30.018 +08:00 [INF] [] [] 連線資訊儲存成功: 新連線
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:36:39.380 +08:00 [DBG] [] [] 視窗設定已儲存
{"SourceContext":"OracleMS.Services.ConfigurationService"}
2025-07-21 23:36:39.381 +08:00 [INF] [] [] 所有活動連線已關閉
{"SourceContext":"OracleMS.Services.ConnectionService"}
2025-07-21 23:43:34.065 +08:00 [DBG] Hosting starting
2025-07-21 23:43:34.089 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 23:43:34.092 +08:00 [INF] Hosting environment: Production
2025-07-21 23:43:34.092 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\oraclems
2025-07-21 23:43:34.092 +08:00 [DBG] Hosting started
2025-07-21 23:43:37.783 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:43:37.899 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:43:38.750 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 23:43:38.751 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 23:43:40.139 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 23:43:40.149 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 23:43:40.149 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:43:40.243 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:43:40.244 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 23:43:40.253 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 23:43:40.253 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 23:43:40.253 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 23:43:43.456 +08:00 [DBG] 視窗設定已儲存
2025-07-21 23:43:43.457 +08:00 [INF] 所有活動連線已關閉
2025-07-21 23:58:07.741 +08:00 [DBG] Hosting starting
2025-07-21 23:58:07.771 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-21 23:58:07.773 +08:00 [INF] Hosting environment: Production
2025-07-21 23:58:07.774 +08:00 [INF] Content root path: F:\Projects\ORMS\OracleMS\oraclems
2025-07-21 23:58:07.774 +08:00 [DBG] Hosting started
2025-07-21 23:58:09.965 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:58:10.066 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:58:11.260 +08:00 [INF] 正在建立 Oracle 連線: 新連線
2025-07-21 23:58:11.261 +08:00 [DBG] 建構連線字串完成，類型: "Basic"
2025-07-21 23:58:12.672 +08:00 [INF] Oracle 連線建立成功: 新連線
2025-07-21 23:58:12.685 +08:00 [INF] 儲存連線資訊: 新連線
2025-07-21 23:58:12.685 +08:00 [DBG] 讀取已儲存的連線清單
2025-07-21 23:58:12.778 +08:00 [INF] 成功讀取 1 個已儲存的連線
2025-07-21 23:58:12.778 +08:00 [INF] 更新現有連線: 新連線
2025-07-21 23:58:12.797 +08:00 [DBG] 設定已儲存: SavedConnections
2025-07-21 23:58:12.797 +08:00 [DBG] 連線清單儲存成功，共 1 個連線
2025-07-21 23:58:12.797 +08:00 [INF] 連線資訊儲存成功: 新連線
2025-07-21 23:58:14.751 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:58:14.754 +08:00 [INF] 正在取得資料庫物件清單，類型: "Table"
2025-07-21 23:58:14.758 +08:00 [INF] 正在執行查詢: 
            SELECT
                o.object_name as Name,
                o.owner as Owner,
   ...
2025-07-21 23:58:14.924 +08:00 [INF] 查詢執行成功，返回 258 筆資料
2025-07-21 23:58:14.928 +08:00 [INF] 成功取得 258 個 "Table" 物件，耗時 175 毫秒
2025-07-21 23:58:16.388 +08:00 [INF] 正在取得資料表定義: EIS_1
2025-07-21 23:58:16.389 +08:00 [INF] 正在取得資料表結構: EIS_1
2025-07-21 23:58:17.181 +08:00 [INF] 資料表結構取得成功: EIS_1
2025-07-21 23:58:17.226 +08:00 [INF] 成功取得資料表定義: EIS_1
2025-07-21 23:58:17.229 +08:00 [INF] 正在產生 CREATE 腳本，物件: EIS_1，類型: "Table"
2025-07-21 23:58:17.230 +08:00 [INF] 正在取得資料表結構: EIS_1
2025-07-21 23:58:17.322 +08:00 [INF] 資料表結構取得成功: EIS_1
2025-07-21 23:58:17.325 +08:00 [INF] CREATE 腳本產生成功，物件: EIS_1，腳本長度: 158
2025-07-21 23:58:17.331 +08:00 [DBG] 已載入編輯器狀態: "Table" EIS_1
2025-07-21 23:58:36.205 +08:00 [DBG] 視窗設定已儲存
2025-07-21 23:58:36.206 +08:00 [INF] 所有活動連線已關閉
